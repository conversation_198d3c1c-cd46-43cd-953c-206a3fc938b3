# Restaurant Tip Calculator

A comprehensive web application for restaurants to fairly distribute tips among employees using multiple calculation methods. Upload Excel files with employee data and export calculated tip distributions.

## 🚀 Features

- **10 Different Calculation Methods** - From basic proportional distribution to advanced performance-based systems
- **Excel Integration** - Upload employee data and export results seamlessly
- **Global Constraints** - Set minimum/maximum tip limits for fair distribution
- **Dynamic Configuration** - Context-sensitive options for each calculation method
- **Real-time Validation** - Immediate feedback on data and configuration issues
- **Professional Interface** - Clean, modern UI with organized formula selection

## 📊 Calculation Methods

### Basic Methods

1. **Proportional by Hours** - Distributes tips based on hours worked proportionally
2. **Fixed Amount** - Gives each employee a fixed amount
3. **Points System** - Uses configurable point columns for distribution
4. **Direct Percentage** - Each employee gets a percentage of their own sales

### Enhanced Methods

5. **Weighted Distribution** - Combines hours, sales, and performance with customizable weights
6. **Role-Based Tips** - Different multipliers for different positions (Manager, Server, Busser, Host)
7. **Shift-Based Tips** - Different rates by time period (Morning, Lunch, Dinner, Late)
8. **Performance-Based Tips** - Bonuses for high performers with configurable thresholds
9. **Section-Based Tips** - Separate pools by restaurant area (Dining Room, Bar, Kitchen)
10. **Service Rating-Based Tips** - Incorporates customer feedback scores

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

```bash
git clone https://github.com/your-username/restaurant-tip-calculator.git
cd restaurant-tip-calculator
npm install
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📋 Excel File Requirements

### Required Columns

Your Excel file must contain these columns:

- **Employee Name** - Name of the employee
- **Hours Worked** - Number of hours worked
- **Sales Generated** - Total sales generated by the employee

### Optional Columns (for enhanced methods)

- **Role** - Employee position (Manager, Server, Busser, Host, etc.)
- **Shift** - Time period (Morning, Lunch, Dinner, Late)
- **Section** - Restaurant area (Dining Room, Bar, Kitchen, etc.)
- **Performance Rating** - Custom performance metrics
- **Service Rating** - Customer service scores (1-5 scale)
- **Points** - Custom point values for point-based systems

## 📖 Usage Guide

### 1. Basic Usage

1. Upload an Excel file with employee data
2. Select a calculation method
3. Configure method-specific options
4. View calculated results
5. Export results to Excel

### 2. Method-Specific Configurations

#### Weighted Distribution

- **Hours Weight** (0-1): Weight given to hours worked
- **Sales Weight** (0-1): Weight given to sales generated
- **Performance Weight** (0-1): Weight given to performance metrics
- **Performance Column**: Optional column for performance data
- _Note: Weights should sum to 1.0_

#### Role-Based Tips

- **Role Column**: Column containing employee roles
- **Default Multipliers**:
  - Manager: 1.5x
  - Server: 1.0x
  - Busser: 0.8x
  - Host: 0.7x

#### Shift-Based Tips

- **Shift Column**: Column containing shift information
- **Default Multipliers**:
  - Morning: 0.8x
  - Lunch: 1.0x
  - Dinner: 1.3x
  - Late: 1.1x

#### Performance-Based Tips

- **Performance Metric**: Choose from Sales, Efficiency, Hours, or Custom
- **Thresholds**:
  - Excellent (≥$1000): 20% bonus
  - Good (≥$500): 10% bonus

#### Section-Based Tips

- **Section Column**: Column containing section/area information
- **Default Allocations**:
  - Dining Room: 60%
  - Bar: 30%
  - Kitchen: 10%

#### Service Rating-Based Tips

- **Service Rating Column**: Column with customer ratings (1-5 scale)
- **Rating Weight**: How much weight to give ratings vs. hours
- **Minimum Rating**: Threshold for tip eligibility

### 3. Global Constraints

Set these optional limits that apply to all methods:

- **Minimum Tip per Employee**: Ensures everyone gets at least this amount
- **Maximum Tip per Employee**: Caps individual tip amounts
- **Minimum Tip per Hour**: Guarantees hourly minimum rates

## 📁 Sample Excel File Structure

```
| Employee Name | Hours Worked | Sales Generated | Role    | Shift  | Section     | Service Rating | Performance |
|---------------|--------------|-----------------|---------|--------|-------------|----------------|-------------|
| John Smith    | 8            | 1200           | Server  | Dinner | Dining Room | 4.5            | 85          |
| Jane Doe      | 6            | 800            | Server  | Lunch  | Dining Room | 4.2            | 78          |
| Mike Johnson  | 8            | 1500           | Manager | Dinner | Dining Room | 4.8            | 92          |
| Sarah Wilson  | 4            | 300            | Busser  | Dinner | Dining Room | 4.0            | 70          |
| Tom Brown     | 6            | 900            | Server  | Dinner | Bar         | 4.6            | 88          |
```

## 🎯 Use Cases

### Fine Dining Restaurant

- Use **Role-Based Tips** with custom multipliers
- Apply **Service Rating-Based Tips** for customer service excellence
- Set **Minimum Tip per Hour** for fair compensation

### Casual Restaurant

- Use **Weighted Distribution** balancing hours and sales
- Apply **Shift-Based Tips** with dinner rush premiums
- Use **Section-Based Tips** for front/back of house separation

### Performance-Driven Establishment

- Use **Performance-Based Tips** with sales thresholds
- Combine with **Weighted Distribution** for comprehensive evaluation
- Set **Maximum Tip per Employee** for budget control

## 🔧 Technical Details

### Built With

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **XLSX** - Excel file processing
- **React Hooks** - State management

### Architecture

- **Modular Design** - Separate calculation functions for each method
- **Type Safety** - Full TypeScript interfaces and validation
- **Error Handling** - Comprehensive validation and user feedback
- **Extensible** - Easy to add new calculation methods

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-method`)
3. Commit changes (`git commit -am 'Add new calculation method'`)
4. Push to branch (`git push origin feature/new-method`)
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, please open an issue on GitHub or contact the development team.

---

**Made with ❤️ for the restaurant industry**
