{"name": "restaurant-tip-calculator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@tailwindcss/cli": "^4.1.11", "autoprefixer": "^10.4.21", "file-saver": "^2.0.5", "next": "15.4.4", "next-intl": "^4.3.4", "react": "19.1.0", "react-dom": "19.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/xlsx": "^0.0.35", "eslint": "^9", "eslint-config-next": "15.4.4", "eslint-config-prettier": "^10.1.8", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "typescript": "^5"}}