export default {
  // Common
  common: {
    ok: 'OK',
    processing: 'Processing...',
    clearFile: 'Clear File',
    selectedFile: 'Selected file:',
    exportToExcel: 'Export to Excel',
    noData: 'No data to export. Please upload an Excel file and calculate tips first.',
    successfulExport: 'Successfully exported data to Excel!',
    failedExport: 'Failed to export data to Excel. Please try again.',
    errorReading: 'Error reading file. Please try again.',
    failedToRead: 'Failed to read Excel file. Please ensure it is a valid .xlsx or .xls format.',
    emptyFile: 'The uploaded Excel file is empty or could not be parsed.',
    calculationError: 'Calculation error:',
    unknownError: 'An unknown error occurred.',
    couldNotCalculate:
      'Could not calculate tips. Please check your Excel file headers and selected formula options.',
    uploadColumns: 'Upload Excel to see columns',
  },

  // Page title and description
  page: {
    title: 'Restaurant Tip Calculator',
    description:
      'Upload your employee data, select a calculation method, and get your tip distributions.',
  },

  // File upload section
  fileUpload: {
    label: 'Upload Employee Data (Excel .xlsx/.xls)',
    instructions:
      'Upload an Excel file with columns like "Employee Name", "Hours Worked", and "Sales Generated" to get started. For the "Points System" method, ensure your Excel includes columns with point values (e.g., "Level Points", "Role Points").',
    foundationalNote: 'This is a foundational component for your Next.js 15 SAAS.',
  },

  // Formula selection
  formula: {
    title: 'Choose Tip Calculation Method',
    selectLabel: 'Select Formula:',
    basicMethods: 'Basic Methods',
    enhancedMethods: 'Enhanced Methods',
    specificOptions: 'Formula Specific Options:',

    // Formula options
    proportionalByHours: 'Tip Pooling: Proportional by Hours Worked',
    fixedAmount: 'Tip Pooling: Fixed Amount per Employee',
    pointsSystem: 'Tip Pooling: Points System',
    directPercentage: 'Tip Splitting: Direct Percentage of Sales',
    weightedDistribution: 'Weighted Distribution (Hours + Sales + Performance)',
    roleBasedTips: 'Role-Based Tips (Different rates by position)',
    shiftBasedTips: 'Shift-Based Tips (Different rates by time)',
    performanceBasedTips: 'Performance-Based Tips (Bonuses for high performers)',
    sectionBasedTips: 'Section-Based Tips (Separate pools by area)',
    serviceRatingBasedTips: 'Service Rating-Based Tips (Customer feedback)',
  },

  // Form labels and inputs
  form: {
    tipPercentage: 'Tip Pool Percentage of Total Sales (e.g., 0.05 for 5%):',
    fixedAmountPerEmployee: 'Fixed Amount per Employee:',
    totalTipPool: 'Total Tip Pool (Optional, will calculate from sales if not provided):',
    totalTipPoolPlaceholder: 'e.g., 1000 or leave blank to calculate from sales',
    pointFactorColumns: 'Select Columns for Point Factors (Hold Ctrl/Cmd to select multiple):',
    pointFactorNote: 'These columns should contain numeric values representing points.',
    directPercentageLabel: 'Percentage of Individual Sales (e.g., 0.10 for 10%):',

    // Weighted distribution
    hoursWeight: 'Hours Weight:',
    salesWeight: 'Sales Weight:',
    performanceWeight: 'Performance Weight:',
    performanceColumn: 'Performance Column (optional):',
    selectPerformanceColumn: 'Select Performance Column',
    weightsNote:
      'Weights should sum to 1.0. Combines hours worked, sales generated, and optional performance metrics.',

    // Role-based
    roleColumn: 'Role Column:',
    selectRoleColumn: 'Select Role Column',
    roleMultipliers:
      'Default role multipliers: Manager (1.5x), Server (1.0x), Busser (0.8x), Host (0.7x)',

    // Shift-based
    shiftColumn: 'Shift Column:',
    selectShiftColumn: 'Select Shift Column',
    shiftMultipliers:
      'Default shift multipliers: Morning (0.8x), Lunch (1.0x), Dinner (1.3x), Late (1.1x)',

    // Performance-based
    performanceMetric: 'Performance Metric:',
    performanceMetricSales: 'Sales Generated',
    performanceMetricEfficiency: 'Sales per Hour',
    performanceMetricHours: 'Hours Worked',
    performanceMetricCustom: 'Custom Column',
    customPerformanceColumn: 'Custom Performance Column:',
    performanceBonuses:
      'Adds bonuses: Excellent performance (≥$1000) gets 20% bonus, Good performance (≥$500) gets 10% bonus',

    // Section-based
    sectionColumn: 'Section Column:',
    selectSectionColumn: 'Select Section Column',
    sectionAllocations: 'Default allocations: Dining Room (60%), Bar (30%), Kitchen (10%)',

    // Service rating
    serviceRatingColumn: 'Service Rating Column:',
    selectRatingColumn: 'Select Rating Column',
    serviceRatingWeight: 'Rating Weight (0-1):',
    minimumRating: 'Minimum Rating:',
    serviceRatingNote:
      'Combines hours worked with customer service ratings. Only employees meeting minimum rating qualify.',
  },

  // Global constraints
  constraints: {
    title: 'Global Constraints (Optional)',
    minimumTipPerEmployee: 'Minimum Tip per Employee ($):',
    maximumTipPerEmployee: 'Maximum Tip per Employee ($):',
    minimumTipPerHour: 'Minimum Tip per Hour ($):',
    noMinimum: '0 = no minimum',
    noMaximum: '0 = no maximum',
    note: 'These constraints apply to all calculation methods and ensure fair minimum/maximum tip amounts.',
  },

  // Analytics and results
  analytics: {
    showAnalytics: 'Show Analytics',
    hideAnalytics: 'Hide Analytics',
    title: '📊 Calculation Analytics',
    totalTipPool: 'Total Tip Pool',
    averageTip: 'Average Tip',
    medianTip: 'Median Tip',
    tipRange: 'Tip Range',
    fairnessAnalysis: '🎯 Fairness Analysis',
    equalityScore: 'Equality Score (Gini Coefficient):',
    tipPerHourVariance: 'Tip Per Hour Variance:',
    veryFair: 'Very Fair',
    moderatelyFair: 'Moderately Fair',
    lessFair: 'Less Fair',
    distributionBreakdown: '📈 Distribution Breakdown',
    byRole: 'By Role',
    byShift: 'By Shift',
    dataQualityWarnings: '⚠️ Data Quality Warnings',
  },

  // Results table
  results: {
    title: 'Calculated Tips',
  },

  // Error messages specific to points system
  errors: {
    pointsSystemSelectColumns:
      'Points System: Please select at least one column for point factors.',
    pointsSystemMissingColumns:
      'Points System: The following columns are missing in the uploaded file:',
  },
} as const;
