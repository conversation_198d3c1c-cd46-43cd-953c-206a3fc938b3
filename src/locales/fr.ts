export default {
  // Common
  common: {
    ok: 'OK',
    processing: 'Traitement en cours...',
    clearFile: 'Effacer le fichier',
    selectedFile: 'Fichier sélectionné :',
    exportToExcel: 'Exporter vers Excel',
    noData:
      "Aucune donnée à exporter. Veuillez télécharger un fichier Excel et calculer les pourboires d'abord.",
    successfulExport: 'Données exportées avec succès vers Excel !',
    failedExport: "Échec de l'exportation des données vers Excel. Veuillez réessayer.",
    errorReading: 'Erreur lors de la lecture du fichier. Veuillez réessayer.',
    failedToRead:
      "Échec de la lecture du fichier Excel. Veuillez vous assurer qu'il s'agit d'un format .xlsx ou .xls valide.",
    emptyFile: "Le fichier Excel téléchargé est vide ou n'a pas pu être analysé.",
    calculationError: 'Erreur de calcul :',
    unknownError: "Une erreur inconnue s'est produite.",
    couldNotCalculate:
      'Impossible de calculer les pourboires. Veuillez vérifier les en-têtes de votre fichier Excel et les options de formule sélectionnées.',
    uploadColumns: 'Téléchargez Excel pour voir les colonnes',
  },

  // Page title and description
  page: {
    title: 'Calculateur de Pourboires de Restaurant',
    description:
      "Téléchargez vos données d'employés, sélectionnez une méthode de calcul et obtenez vos distributions de pourboires.",
  },

  // File upload section
  fileUpload: {
    label: "Télécharger les Données d'Employés (Excel .xlsx/.xls)",
    instructions:
      'Téléchargez un fichier Excel avec des colonnes comme "Nom de l\'employé", "Heures travaillées" et "Ventes générées" pour commencer. Pour la méthode "Système de points", assurez-vous que votre Excel inclut des colonnes avec des valeurs de points (par exemple, "Points de niveau", "Points de rôle").',
    foundationalNote: 'Ceci est un composant fondamental pour votre SAAS Next.js 15.',
  },

  // Formula selection
  formula: {
    title: 'Choisir la Méthode de Calcul des Pourboires',
    selectLabel: 'Sélectionner la Formule :',
    basicMethods: 'Méthodes de Base',
    enhancedMethods: 'Méthodes Avancées',
    specificOptions: 'Options Spécifiques à la Formule :',

    // Formula options
    proportionalByHours: 'Mise en Commun des Pourboires : Proportionnel aux Heures Travaillées',
    fixedAmount: 'Mise en Commun des Pourboires : Montant Fixe par Employé',
    pointsSystem: 'Mise en Commun des Pourboires : Système de Points',
    directPercentage: 'Partage des Pourboires : Pourcentage Direct des Ventes',
    weightedDistribution: 'Distribution Pondérée (Heures + Ventes + Performance)',
    roleBasedTips: 'Pourboires Basés sur le Rôle (Taux différents par poste)',
    shiftBasedTips: "Pourboires Basés sur l'Équipe (Taux différents par horaire)",
    performanceBasedTips: 'Pourboires Basés sur la Performance (Bonus pour les hauts performeurs)',
    sectionBasedTips: 'Pourboires Basés sur la Section (Pools séparés par zone)',
    serviceRatingBasedTips: "Pourboires Basés sur l'Évaluation du Service (Commentaires clients)",
  },

  // Form labels and inputs
  form: {
    tipPercentage: 'Pourcentage du Pool de Pourboires sur les Ventes Totales (ex. 0,05 pour 5%) :',
    fixedAmountPerEmployee: 'Montant Fixe par Employé :',
    totalTipPool:
      'Pool Total de Pourboires (Optionnel, sera calculé à partir des ventes si non fourni) :',
    totalTipPoolPlaceholder: 'ex. 1000 ou laisser vide pour calculer à partir des ventes',
    pointFactorColumns:
      'Sélectionner les Colonnes pour les Facteurs de Points (Maintenez Ctrl/Cmd pour sélectionner plusieurs) :',
    pointFactorNote:
      'Ces colonnes doivent contenir des valeurs numériques représentant des points.',
    directPercentageLabel: 'Pourcentage des Ventes Individuelles (ex. 0,10 pour 10%) :',

    // Weighted distribution
    hoursWeight: 'Poids des Heures :',
    salesWeight: 'Poids des Ventes :',
    performanceWeight: 'Poids de la Performance :',
    performanceColumn: 'Colonne de Performance (optionnel) :',
    selectPerformanceColumn: 'Sélectionner la Colonne de Performance',
    weightsNote:
      'Les poids doivent totaliser 1,0. Combine les heures travaillées, les ventes générées et les métriques de performance optionnelles.',

    // Role-based
    roleColumn: 'Colonne de Rôle :',
    selectRoleColumn: 'Sélectionner la Colonne de Rôle',
    roleMultipliers:
      'Multiplicateurs de rôle par défaut : Manager (1,5x), Serveur (1,0x), Aide-serveur (0,8x), Hôte (0,7x)',

    // Shift-based
    shiftColumn: "Colonne d'Équipe :",
    selectShiftColumn: "Sélectionner la Colonne d'Équipe",
    shiftMultipliers:
      "Multiplicateurs d'équipe par défaut : Matin (0,8x), Déjeuner (1,0x), Dîner (1,3x), Tard (1,1x)",

    // Performance-based
    performanceMetric: 'Métrique de Performance :',
    performanceMetricSales: 'Ventes Générées',
    performanceMetricEfficiency: 'Ventes par Heure',
    performanceMetricHours: 'Heures Travaillées',
    performanceMetricCustom: 'Colonne Personnalisée',
    customPerformanceColumn: 'Colonne de Performance Personnalisée :',
    performanceBonuses:
      'Ajoute des bonus : Performance excellente (≥1000$) obtient 20% de bonus, Bonne performance (≥500$) obtient 10% de bonus',

    // Section-based
    sectionColumn: 'Colonne de Section :',
    selectSectionColumn: 'Sélectionner la Colonne de Section',
    sectionAllocations: 'Allocations par défaut : Salle à manger (60%), Bar (30%), Cuisine (10%)',

    // Service rating
    serviceRatingColumn: "Colonne d'Évaluation du Service :",
    selectRatingColumn: "Sélectionner la Colonne d'Évaluation",
    serviceRatingWeight: "Poids de l'Évaluation (0-1) :",
    minimumRating: 'Évaluation Minimale :',
    serviceRatingNote:
      "Combine les heures travaillées avec les évaluations du service client. Seuls les employés atteignant l'évaluation minimale sont qualifiés.",
  },

  // Global constraints
  constraints: {
    title: 'Contraintes Globales (Optionnel)',
    minimumTipPerEmployee: 'Pourboire Minimum par Employé ($) :',
    maximumTipPerEmployee: 'Pourboire Maximum par Employé ($) :',
    minimumTipPerHour: 'Pourboire Minimum par Heure ($) :',
    noMinimum: '0 = pas de minimum',
    noMaximum: '0 = pas de maximum',
    note: "Ces contraintes s'appliquent à toutes les méthodes de calcul et garantissent des montants de pourboires minimum/maximum équitables.",
  },

  // Analytics and results
  analytics: {
    showAnalytics: 'Afficher les Analyses',
    hideAnalytics: 'Masquer les Analyses',
    title: '📊 Analyses de Calcul',
    totalTipPool: 'Pool Total de Pourboires',
    averageTip: 'Pourboire Moyen',
    medianTip: 'Pourboire Médian',
    tipRange: 'Plage de Pourboires',
    fairnessAnalysis: "🎯 Analyse d'Équité",
    equalityScore: "Score d'Égalité (Coefficient de Gini) :",
    tipPerHourVariance: 'Variance du Pourboire par Heure :',
    veryFair: 'Très Équitable',
    moderatelyFair: 'Modérément Équitable',
    lessFair: 'Moins Équitable',
    distributionBreakdown: '📈 Répartition de la Distribution',
    byRole: 'Par Rôle',
    byShift: 'Par Équipe',
    dataQualityWarnings: '⚠️ Avertissements de Qualité des Données',
  },

  // Results table
  results: {
    title: 'Pourboires Calculés',
  },

  // Error messages specific to points system
  errors: {
    pointsSystemSelectColumns:
      'Système de Points : Veuillez sélectionner au moins une colonne pour les facteurs de points.',
    pointsSystemMissingColumns:
      'Système de Points : Les colonnes suivantes sont manquantes dans le fichier téléchargé :',
  },
} as const;
