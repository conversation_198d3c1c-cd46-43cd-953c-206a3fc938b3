/**
 * Interface for the raw employee data parsed from Excel.
 * Assumes column names as keys.
 */
export interface EmployeeData {
  'Employee Name': string;
  'Hours Worked': number;
  'Sales Generated': number;
  // Add other potential columns here for point factors:
  // 'Employee Level Points'?: number;
  // 'Role Points'?: number;
  // 'Work Hours Points'?: number;
  // 'Shift Period Points'?: number;
  [key: string]: string | number | undefined; // Allow for additional properties with specific types
}

/**
 * Interface for the calculated tip results for each employee.
 */
export interface CalculatedTipResult {
  'Employee Name': string;
  'Original Hours': number | string; // Can be 'N/A' if data is missing
  'Original Sales': number | string; // Can be 'N/A' if data is missing
  'Calculated Tip': string; // Formatted to 2 decimal places as a string
}

/**
 * Interface for role-based tier configurations
 */
export interface RoleTier {
  multiplier: number;
  minimumTip?: number;
  maximumTip?: number;
}

/**
 * Interface for shift multiplier configurations
 */
export interface ShiftMultipliers {
  morning: number;
  lunch: number;
  dinner: number;
  late: number;
  [key: string]: number; // Allow custom shift names
}

/**
 * Interface for performance threshold configurations
 */
export interface PerformanceThresholds {
  excellent: { threshold: number; bonus: number };
  good: { threshold: number; bonus: number };
}

/**
 * Interface for tip calculation summary and analytics
 */
export interface TipCalculationSummary {
  totalTipPool: number;
  averageTip: number;
  medianTip: number;
  tipRange: { min: number; max: number };
  employeesAffectedByConstraints: number;
  distributionByRole?: { [role: string]: { count: number; totalTips: number } };
  distributionByShift?: { [shift: string]: { count: number; totalTips: number } };
}

/**
 * Interface for fairness analysis metrics
 */
export interface FairnessMetrics {
  giniCoefficient: number; // 0 = perfectly equal, 1 = maximally unequal
  tipPerHourVariance: number;
  roleFairnessScore: number;
  shiftFairnessScore: number;
}

/**
 * Interface for data validation results
 */
export interface DataValidationResult {
  isValid: boolean;
  warnings: string[];
  errors: string[];
}

/**
 * Interface for options specific to each tip calculation formula.
 */
export interface FormulaOptions {
  // Basic options
  tipPercentage?: number; // For proportionalByHours, directPercentage, etc. (e.g., 0.05 for 5%)
  fixedAmountPerEmployee?: number; // For fixedAmount method
  totalTipPool?: number; // Can be explicitly provided or calculated from sales
  pointsColumn?: string; // For point-based system, specifies a single column name for points
  pointFactorColumns?: string[]; // For point-based system, specifies multiple columns whose values sum up to total points

  // Enhanced options for new methods
  // Weighted distribution
  hoursWeight?: number; // Weight for hours worked (0-1)
  salesWeight?: number; // Weight for sales generated (0-1)
  performanceWeight?: number; // Weight for performance metrics (0-1)
  performanceColumn?: string; // Column name for performance data

  // Role-based tiers
  roleTiers?: { [roleName: string]: RoleTier };
  roleColumn?: string; // Column name for employee roles

  // Shift-based adjustments
  shiftMultipliers?: ShiftMultipliers;
  shiftColumn?: string; // Column name for shift information

  // Performance-based bonuses
  performanceThresholds?: PerformanceThresholds;
  performanceMetric?: 'sales' | 'efficiency' | 'hours' | 'custom';

  // Min/Max guarantees
  minimumTipPerEmployee?: number;
  maximumTipPerEmployee?: number;
  minimumTipPerHour?: number;

  // Section-based pooling
  sectionColumn?: string;
  sectionAllocations?: { [section: string]: number };

  // Customer service ratings
  serviceRatingColumn?: string;
  serviceRatingWeight?: number;
  minimumRatingThreshold?: number;

  // Historical performance
  historicalWeight?: number;
  lookbackPeriod?: number;
}

/**
 * Validates data quality and provides warnings for potential issues
 */
export const validateDataQuality = (data: EmployeeData[]): DataValidationResult => {
  const warnings: string[] = [];
  const errors: string[] = [];

  if (!data || data.length === 0) {
    errors.push('No data provided');
    return { isValid: false, warnings, errors };
  }

  // Check for duplicate employee names
  const employeeNames = data.map((row) => row['Employee Name']);
  const duplicates = employeeNames.filter((name, index) => employeeNames.indexOf(name) !== index);
  if (duplicates.length > 0) {
    warnings.push(`Duplicate employee names found: ${[...new Set(duplicates)].join(', ')}`);
  }

  // Check for negative values
  data.forEach((row, index) => {
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));

    if (!isNaN(hours) && hours < 0) {
      errors.push(`Row ${index + 1}: Negative hours worked (${hours}) for ${row['Employee Name']}`);
    }
    if (!isNaN(sales) && sales < 0) {
      errors.push(`Row ${index + 1}: Negative sales (${sales}) for ${row['Employee Name']}`);
    }
  });

  // Check for missing data patterns
  const missingHours = data.filter((row) => isNaN(parseFloat(String(row['Hours Worked']))));
  const missingSales = data.filter((row) => isNaN(parseFloat(String(row['Sales Generated']))));

  if (missingHours.length > 0) {
    warnings.push(`${missingHours.length} employees have missing hours data`);
  }
  if (missingSales.length > 0) {
    warnings.push(`${missingSales.length} employees have missing sales data`);
  }

  return { isValid: errors.length === 0, warnings, errors };
};

/**
 * Generates calculation summary and analytics
 */
export const generateCalculationSummary = (
  results: CalculatedTipResult[],
  data: EmployeeData[],
  options: FormulaOptions,
): TipCalculationSummary => {
  const tips = results.map((r) => parseFloat(r['Calculated Tip']));
  const totalTipPool = tips.reduce((sum, tip) => sum + tip, 0);
  const averageTip = tips.length > 0 ? totalTipPool / tips.length : 0;

  // Calculate median
  const sortedTips = [...tips].sort((a, b) => a - b);
  const medianTip =
    sortedTips.length > 0
      ? sortedTips.length % 2 === 0
        ? (sortedTips[sortedTips.length / 2 - 1] + sortedTips[sortedTips.length / 2]) / 2
        : sortedTips[Math.floor(sortedTips.length / 2)]
      : 0;

  const tipRange = {
    min: Math.min(...tips),
    max: Math.max(...tips),
  };

  // Count employees affected by constraints
  const employeesAffectedByConstraints = 0;
  // This would require tracking which employees had their tips modified by constraints
  // For now, we'll estimate based on min/max values matching constraint values

  const summary: TipCalculationSummary = {
    totalTipPool,
    averageTip,
    medianTip,
    tipRange,
    employeesAffectedByConstraints,
  };

  // Add role distribution if role column exists
  if (options.roleColumn) {
    summary.distributionByRole = {};
    data.forEach((row, index) => {
      const role = String(row[options.roleColumn!]);
      const tip = parseFloat(results[index]['Calculated Tip']);

      if (!summary.distributionByRole![role]) {
        summary.distributionByRole![role] = { count: 0, totalTips: 0 };
      }
      summary.distributionByRole![role].count++;
      summary.distributionByRole![role].totalTips += tip;
    });
  }

  // Add shift distribution if shift column exists
  if (options.shiftColumn) {
    summary.distributionByShift = {};
    data.forEach((row, index) => {
      const shift = String(row[options.shiftColumn!]);
      const tip = parseFloat(results[index]['Calculated Tip']);

      if (!summary.distributionByShift![shift]) {
        summary.distributionByShift![shift] = { count: 0, totalTips: 0 };
      }
      summary.distributionByShift![shift].count++;
      summary.distributionByShift![shift].totalTips += tip;
    });
  }

  return summary;
};

/**
 * Analyzes fairness metrics of tip distribution
 */
export const analyzeFairness = (
  results: CalculatedTipResult[],
  data: EmployeeData[],
): FairnessMetrics => {
  const tips = results.map((r) => parseFloat(r['Calculated Tip']));

  // Calculate Gini coefficient
  const sortedTips = [...tips].sort((a, b) => a - b);
  const n = sortedTips.length;
  let giniSum = 0;

  for (let i = 0; i < n; i++) {
    giniSum += (2 * (i + 1) - n - 1) * sortedTips[i];
  }

  const giniCoefficient = n > 1 ? giniSum / (n * tips.reduce((sum, tip) => sum + tip, 0)) : 0;

  // Calculate tip per hour variance
  const tipPerHourRates = results
    .map((result, index) => {
      const tip = parseFloat(result['Calculated Tip']);
      const employeeHours = parseFloat(String(data[index]['Hours Worked']));
      return !isNaN(employeeHours) && employeeHours > 0 ? tip / employeeHours : 0;
    })
    .filter((rate) => rate > 0);

  const avgTipPerHour =
    tipPerHourRates.reduce((sum, rate) => sum + rate, 0) / tipPerHourRates.length;
  const tipPerHourVariance =
    tipPerHourRates.reduce((sum, rate) => sum + Math.pow(rate - avgTipPerHour, 2), 0) /
    tipPerHourRates.length;

  return {
    giniCoefficient: Math.abs(giniCoefficient),
    tipPerHourVariance,
    roleFairnessScore: 0.8, // Placeholder - would need role analysis
    shiftFairnessScore: 0.7, // Placeholder - would need shift analysis
  };
};

/**
 * Validates if the necessary headers exist in the first row of parsed data.
 * @param {EmployeeData[]} data - The parsed Excel data.
 * @returns {boolean} True if all required headers are present, false otherwise.
 */
const validateRequiredHeaders = (data: EmployeeData[]): boolean => {
  if (!data || data.length === 0) {
    return false;
  }
  const firstRowKeys = Object.keys(data[0] || {});
  return (
    firstRowKeys.includes('Employee Name') &&
    firstRowKeys.includes('Hours Worked') &&
    firstRowKeys.includes('Sales Generated')
  );
};

/**
 * Calculates tip pool based on total sales.
 * @param {number} totalSales - The sum of all sales generated.
 * @param {number} tipPercentage - The percentage of total sales to form the tip pool (e.g., 0.05 for 5%).
 * @returns {number} The calculated total tip pool.
 */
const calculateTotalTipPoolFromSales = (totalSales: number, tipPercentage: number): number => {
  return totalSales * tipPercentage;
};

/**
 * Applies minimum and maximum tip constraints to a calculated tip amount.
 * @param {number} calculatedTip - The originally calculated tip amount.
 * @param {number} hours - Hours worked by the employee.
 * @param {FormulaOptions} options - Options containing min/max constraints.
 * @returns {number} The constrained tip amount.
 */
const applyTipConstraints = (
  calculatedTip: number,
  hours: number,
  options: FormulaOptions,
): number => {
  let constrainedTip = calculatedTip;

  // Apply minimum per employee
  if (options.minimumTipPerEmployee !== undefined && options.minimumTipPerEmployee > 0) {
    constrainedTip = Math.max(constrainedTip, options.minimumTipPerEmployee);
  }

  // Apply minimum per hour
  if (
    options.minimumTipPerHour !== undefined &&
    options.minimumTipPerHour > 0 &&
    !isNaN(hours) &&
    hours > 0
  ) {
    const minTipForHours = options.minimumTipPerHour * hours;
    constrainedTip = Math.max(constrainedTip, minTipForHours);
  }

  // Apply maximum per employee (only if it's a meaningful constraint > 0)
  if (options.maximumTipPerEmployee !== undefined && options.maximumTipPerEmployee > 0) {
    constrainedTip = Math.min(constrainedTip, options.maximumTipPerEmployee);
  }

  return constrainedTip;
};

/**
 * Normalizes a value to a 0-1 scale based on min and max values in the dataset.
 * @param {number} value - The value to normalize.
 * @param {number} min - The minimum value in the dataset.
 * @param {number} max - The maximum value in the dataset.
 * @returns {number} The normalized value (0-1).
 */
const normalizeValue = (value: number, min: number, max: number): number => {
  if (max === min) return 0.5; // If all values are the same, give equal weight
  return (value - min) / (max - min);
};

/**
 * Tip Pooling Method: Distributes tips proportionally based on hours worked.
 * Assumes a total tip pool is either provided or calculated from total sales.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including tipPercentage or totalTipPool.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculatePoolByHours = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  let totalSales = 0;
  let totalHours = 0;

  data.forEach((row) => {
    const sales = parseFloat(String(row['Sales Generated']));
    const hours = parseFloat(String(row['Hours Worked']));

    if (!isNaN(sales)) {
      totalSales += sales;
    }
    if (!isNaN(hours)) {
      totalHours += hours;
    }
  });

  // Determine the total tip pool
  let totalTipPool = options.totalTipPool || 0;
  if (totalTipPool === 0 && options.tipPercentage !== undefined) {
    totalTipPool = calculateTotalTipPoolFromSales(totalSales, options.tipPercentage);
  }

  if (totalTipPool === 0) {
    console.warn(
      'calculatePoolByHours: No totalTipPool or tipPercentage provided. Tips will be 0.',
    );
  }

  return data.map((row) => {
    const employeeName = row['Employee Name'];
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));

    let calculatedTip = 0;
    if (totalHours > 0 && !isNaN(hours)) {
      calculatedTip = (hours / totalHours) * totalTipPool;
    }

    // Apply constraints
    calculatedTip = applyTipConstraints(calculatedTip, hours, options);

    return {
      'Employee Name': employeeName,
      'Original Hours': isNaN(hours) ? 'N/A' : hours,
      'Original Sales': isNaN(sales) ? 'N/A' : sales,
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Tip Pooling Method: Distributes a fixed amount of tips to each eligible employee.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including fixedAmountPerEmployee.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculatePoolFixedAmount = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  if (options.fixedAmountPerEmployee === undefined || options.fixedAmountPerEmployee < 0) {
    console.warn(
      'calculatePoolFixedAmount: fixedAmountPerEmployee is not provided or invalid. Tips will be 0.',
    );
    return data.map((row) => ({
      'Employee Name': row['Employee Name'],
      'Original Hours': row['Hours Worked'],
      'Original Sales': row['Sales Generated'],
      'Calculated Tip': '0.00',
    }));
  }

  return data.map((row) => {
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));

    let calculatedTip = options.fixedAmountPerEmployee!;

    // Apply constraints
    calculatedTip = applyTipConstraints(calculatedTip, hours, options);

    return {
      'Employee Name': row['Employee Name'],
      'Original Hours': isNaN(hours) ? 'N/A' : hours,
      'Original Sales': isNaN(sales) ? 'N/A' : sales,
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Tip Pooling Method: Distributes tips based on a point system.
 * Can use a single 'Points' column or sum values from multiple 'pointFactorColumns'.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including totalTipPool, pointsColumn, or pointFactorColumns.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculatePoolByPoints = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  let totalPoints = 0;
  const employeePointsMap = new Map<string, number>(); // To store calculated points for each employee

  // Determine which columns to use for points
  const pointColumns: string[] = [];
  if (options.pointFactorColumns && options.pointFactorColumns.length > 0) {
    pointColumns.push(...options.pointFactorColumns);
  } else if (options.pointsColumn) {
    pointColumns.push(options.pointsColumn);
  } else {
    console.error(
      `calculatePoolByPoints: No 'pointsColumn' or 'pointFactorColumns' provided for point system calculation.`,
    );
    return [];
  }

  // Validate that at least one of the specified point columns exists in the data
  const hasAnyPointColumn = pointColumns.some(
    (col) => data[0] && Object.keys(data[0]).includes(col),
  );
  if (!hasAnyPointColumn) {
    console.error(
      `calculatePoolByPoints: None of the specified point columns (${pointColumns.join(', ')}) found in the Excel data.`,
    );
    return [];
  }

  // Calculate total points for the pool and individual employee points
  data.forEach((row) => {
    let employeeTotalPoints = 0;
    pointColumns.forEach((col) => {
      const points = parseFloat(String(row[col]));
      if (!isNaN(points)) {
        employeeTotalPoints += points;
      }
    });
    employeePointsMap.set(row['Employee Name'], employeeTotalPoints);
    totalPoints += employeeTotalPoints;
  });

  let totalTipPool = options.totalTipPool || 0;
  if (totalTipPool === 0 && options.tipPercentage !== undefined) {
    let totalSales = 0;
    data.forEach((row) => {
      const sales = parseFloat(String(row['Sales Generated']));
      if (!isNaN(sales)) totalSales += sales;
    });
    totalTipPool = calculateTotalTipPoolFromSales(totalSales, options.tipPercentage);
  } else if (totalTipPool === 0) {
    console.warn(
      'calculatePoolByPoints: No totalTipPool or tipPercentage provided. Tips will be 0.',
    );
  }

  return data.map((row) => {
    const employeeName = row['Employee Name'];
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));
    const employeeCalculatedPoints = employeePointsMap.get(employeeName) || 0;

    let calculatedTip = 0;
    if (totalPoints > 0 && employeeCalculatedPoints > 0) {
      calculatedTip = (employeeCalculatedPoints / totalPoints) * totalTipPool;
    }

    // Apply constraints
    calculatedTip = applyTipConstraints(calculatedTip, hours, options);

    return {
      'Employee Name': employeeName,
      'Original Hours': isNaN(hours) ? 'N/A' : hours,
      'Original Sales': isNaN(sales) ? 'N/A' : sales,
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Tip Splitting Method: Each employee receives a direct percentage of their own sales.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including tipPercentage.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculateSplitDirectPercentage = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  if (options.tipPercentage === undefined || options.tipPercentage < 0) {
    console.warn(
      'calculateSplitDirectPercentage: tipPercentage is not provided or invalid. Tips will be 0.',
    );
    return data.map((row) => ({
      'Employee Name': row['Employee Name'],
      'Original Hours': row['Hours Worked'],
      'Original Sales': row['Sales Generated'],
      'Calculated Tip': '0.00',
    }));
  }

  return data.map((row) => {
    const employeeName = row['Employee Name'];
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));

    let calculatedTip = 0;
    if (!isNaN(sales)) {
      calculatedTip = sales * options.tipPercentage!;
    }

    // Apply constraints
    calculatedTip = applyTipConstraints(calculatedTip, hours, options);

    return {
      'Employee Name': employeeName,
      'Original Hours': isNaN(hours) ? 'N/A' : hours,
      'Original Sales': isNaN(sales) ? 'N/A' : sales,
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Enhanced Method: Weighted distribution combining hours, sales, and performance.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including weights and performance column.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculateWeightedDistribution = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  // Default weights if not provided
  const hoursWeight = options.hoursWeight ?? 0.4;
  const salesWeight = options.salesWeight ?? 0.4;
  const performanceWeight = options.performanceWeight ?? 0.2;

  // Validate weights sum to 1
  const totalWeight = hoursWeight + salesWeight + performanceWeight;
  if (Math.abs(totalWeight - 1.0) > 0.01) {
    console.warn(`Weights sum to ${totalWeight}, normalizing to 1.0`);
  }

  // Calculate totals and ranges for normalization
  let totalSales = 0;
  let minHours = Infinity,
    maxHours = -Infinity;
  let minSales = Infinity,
    maxSales = -Infinity;
  let minPerformance = Infinity,
    maxPerformance = -Infinity;

  data.forEach((row) => {
    const sales = parseFloat(String(row['Sales Generated']));
    const hours = parseFloat(String(row['Hours Worked']));
    const performance = options.performanceColumn
      ? parseFloat(String(row[options.performanceColumn]))
      : 1;

    if (!isNaN(sales)) {
      totalSales += sales;
      minSales = Math.min(minSales, sales);
      maxSales = Math.max(maxSales, sales);
    }
    if (!isNaN(hours)) {
      minHours = Math.min(minHours, hours);
      maxHours = Math.max(maxHours, hours);
    }
    if (!isNaN(performance)) {
      minPerformance = Math.min(minPerformance, performance);
      maxPerformance = Math.max(maxPerformance, performance);
    }
  });

  // Determine total tip pool
  let totalTipPool = options.totalTipPool || 0;
  if (totalTipPool === 0 && options.tipPercentage !== undefined) {
    totalTipPool = calculateTotalTipPoolFromSales(totalSales, options.tipPercentage);
  }

  // Calculate weighted scores for each employee
  const employeeScores = data.map((row) => {
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));
    const performance = options.performanceColumn
      ? parseFloat(String(row[options.performanceColumn]))
      : 1;

    const normalizedHours = !isNaN(hours) ? normalizeValue(hours, minHours, maxHours) : 0;
    const normalizedSales = !isNaN(sales) ? normalizeValue(sales, minSales, maxSales) : 0;
    const normalizedPerformance = !isNaN(performance)
      ? normalizeValue(performance, minPerformance, maxPerformance)
      : 0.5;

    const weightedScore =
      normalizedHours * hoursWeight +
      normalizedSales * salesWeight +
      normalizedPerformance * performanceWeight;

    return {
      employeeName: row['Employee Name'],
      hours,
      sales,
      performance,
      weightedScore,
    };
  });

  const totalWeightedScore = employeeScores.reduce((sum, emp) => sum + emp.weightedScore, 0);

  return employeeScores.map((emp) => {
    let calculatedTip = 0;
    if (totalWeightedScore > 0) {
      calculatedTip = (emp.weightedScore / totalWeightedScore) * totalTipPool;
    }

    // Apply constraints
    calculatedTip = applyTipConstraints(calculatedTip, emp.hours, options);

    return {
      'Employee Name': emp.employeeName,
      'Original Hours': isNaN(emp.hours) ? 'N/A' : emp.hours,
      'Original Sales': isNaN(emp.sales) ? 'N/A' : emp.sales,
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Enhanced Method: Role-based tip distribution with different multipliers for different positions.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including role tiers and role column.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculateRoleBasedTips = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  if (!options.roleColumn || !options.roleTiers) {
    console.error('calculateRoleBasedTips: roleColumn and roleTiers are required');
    return [];
  }

  // Calculate base distribution (using hours as default)
  let totalSales = 0;

  data.forEach((row) => {
    const sales = parseFloat(String(row['Sales Generated']));
    if (!isNaN(sales)) totalSales += sales;
  });

  let totalTipPool = options.totalTipPool || 0;
  if (totalTipPool === 0 && options.tipPercentage !== undefined) {
    totalTipPool = calculateTotalTipPoolFromSales(totalSales, options.tipPercentage);
  }

  // Calculate total weighted hours (hours * role multiplier)
  let totalWeightedHours = 0;
  data.forEach((row) => {
    const hours = parseFloat(String(row['Hours Worked']));
    const role = String(row[options.roleColumn!]);
    const roleTier = options.roleTiers![role];
    const multiplier = roleTier?.multiplier || 1;

    if (!isNaN(hours)) {
      totalWeightedHours += hours * multiplier;
    }
  });

  return data.map((row) => {
    const employeeName = row['Employee Name'];
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));
    const role = String(row[options.roleColumn!]);
    const roleTier = options.roleTiers![role];

    let calculatedTip = 0;
    if (!isNaN(hours) && totalWeightedHours > 0 && roleTier) {
      const weightedHours = hours * roleTier.multiplier;
      calculatedTip = (weightedHours / totalWeightedHours) * totalTipPool;

      // Apply role-specific min/max
      if (roleTier.minimumTip !== undefined) {
        calculatedTip = Math.max(calculatedTip, roleTier.minimumTip);
      }
      if (roleTier.maximumTip !== undefined) {
        calculatedTip = Math.min(calculatedTip, roleTier.maximumTip);
      }
    }

    // Apply general constraints
    calculatedTip = applyTipConstraints(calculatedTip, hours, options);

    return {
      'Employee Name': employeeName,
      'Original Hours': isNaN(hours) ? 'N/A' : hours,
      'Original Sales': isNaN(sales) ? 'N/A' : sales,
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Enhanced Method: Shift-based tip distribution with different multipliers for different shifts.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including shift multipliers and shift column.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculateShiftBasedTips = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  if (!options.shiftColumn || !options.shiftMultipliers) {
    console.error('calculateShiftBasedTips: shiftColumn and shiftMultipliers are required');
    return [];
  }

  // Calculate totals
  let totalSales = 0;
  data.forEach((row) => {
    const sales = parseFloat(String(row['Sales Generated']));
    if (!isNaN(sales)) totalSales += sales;
  });

  let totalTipPool = options.totalTipPool || 0;
  if (totalTipPool === 0 && options.tipPercentage !== undefined) {
    totalTipPool = calculateTotalTipPoolFromSales(totalSales, options.tipPercentage);
  }

  // Calculate total weighted hours (hours * shift multiplier)
  let totalWeightedHours = 0;
  data.forEach((row) => {
    const hours = parseFloat(String(row['Hours Worked']));
    const shift = String(row[options.shiftColumn!]).toLowerCase();
    const multiplier = options.shiftMultipliers![shift] || 1;

    if (!isNaN(hours)) {
      totalWeightedHours += hours * multiplier;
    }
  });

  return data.map((row) => {
    const employeeName = row['Employee Name'];
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));
    const shift = String(row[options.shiftColumn!]).toLowerCase();
    const multiplier = options.shiftMultipliers![shift] || 1;

    let calculatedTip = 0;
    if (!isNaN(hours) && totalWeightedHours > 0) {
      const weightedHours = hours * multiplier;
      calculatedTip = (weightedHours / totalWeightedHours) * totalTipPool;
    }

    // Apply constraints
    calculatedTip = applyTipConstraints(calculatedTip, hours, options);

    return {
      'Employee Name': employeeName,
      'Original Hours': isNaN(hours) ? 'N/A' : hours,
      'Original Sales': isNaN(sales) ? 'N/A' : sales,
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Enhanced Method: Performance-based tip distribution with bonuses for high performers.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including performance thresholds and metric.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculatePerformanceBasedTips = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  if (!options.performanceThresholds) {
    console.error('calculatePerformanceBasedTips: performanceThresholds are required');
    return [];
  }

  // Start with base calculation (proportional by hours)
  const baseResults = calculatePoolByHours(data, options);

  return baseResults.map((result, index) => {
    const row = data[index];
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));

    // Determine performance metric value
    let performanceValue = 0;
    switch (options.performanceMetric) {
      case 'sales':
        performanceValue = sales;
        break;
      case 'efficiency':
        performanceValue = !isNaN(sales) && !isNaN(hours) && hours > 0 ? sales / hours : 0;
        break;
      case 'hours':
        performanceValue = hours;
        break;
      case 'custom':
        if (options.performanceColumn) {
          performanceValue = parseFloat(String(row[options.performanceColumn]));
        }
        break;
      default:
        performanceValue = sales; // Default to sales
    }

    let calculatedTip = parseFloat(result['Calculated Tip']);
    let bonusMultiplier = 1;

    // Apply performance bonuses
    if (options.performanceThresholds) {
      if (performanceValue >= options.performanceThresholds.excellent.threshold) {
        bonusMultiplier = 1 + options.performanceThresholds.excellent.bonus;
      } else if (performanceValue >= options.performanceThresholds.good.threshold) {
        bonusMultiplier = 1 + options.performanceThresholds.good.bonus;
      }
    }

    calculatedTip *= bonusMultiplier;

    // Apply constraints
    calculatedTip = applyTipConstraints(calculatedTip, hours, options);

    return {
      'Employee Name': result['Employee Name'],
      'Original Hours': result['Original Hours'],
      'Original Sales': result['Original Sales'],
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Enhanced Method: Section-based tip pooling for different restaurant areas.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including section column and allocations.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculateSectionBasedTips = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  if (!options.sectionColumn || !options.sectionAllocations) {
    console.error('calculateSectionBasedTips: sectionColumn and sectionAllocations are required');
    return [];
  }

  // Calculate total tip pool
  let totalSales = 0;
  data.forEach((row) => {
    const sales = parseFloat(String(row['Sales Generated']));
    if (!isNaN(sales)) totalSales += sales;
  });

  let totalTipPool = options.totalTipPool || 0;
  if (totalTipPool === 0 && options.tipPercentage !== undefined) {
    totalTipPool = calculateTotalTipPoolFromSales(totalSales, options.tipPercentage);
  }

  // Group employees by section
  const sectionGroups: { [section: string]: EmployeeData[] } = {};
  data.forEach((row) => {
    const section = String(row[options.sectionColumn!]);
    if (!sectionGroups[section]) {
      sectionGroups[section] = [];
    }
    sectionGroups[section].push(row);
  });

  const results: CalculatedTipResult[] = [];

  // Calculate tips for each section
  Object.keys(sectionGroups).forEach((section) => {
    const sectionEmployees = sectionGroups[section];
    const sectionAllocation = options.sectionAllocations![section] || 0;
    const sectionTipPool = totalTipPool * sectionAllocation;

    // Use proportional by hours within each section
    const sectionOptions = { ...options, totalTipPool: sectionTipPool };
    const sectionResults = calculatePoolByHours(sectionEmployees, sectionOptions);
    results.push(...sectionResults);
  });

  return results;
};

/**
 * Enhanced Method: Customer service rating-based tip distribution.
 * @param {EmployeeData[]} data - The parsed employee data.
 * @param {FormulaOptions} options - Options including service rating column and weight.
 * @returns {CalculatedTipResult[]} Array of calculated tip results.
 */
export const calculateServiceRatingBasedTips = (
  data: EmployeeData[],
  options: FormulaOptions,
): CalculatedTipResult[] => {
  if (!options.serviceRatingColumn) {
    console.error('calculateServiceRatingBasedTips: serviceRatingColumn is required');
    return [];
  }

  const serviceWeight = options.serviceRatingWeight ?? 0.3;
  const hoursWeight = 1 - serviceWeight;
  const minimumRating = options.minimumRatingThreshold ?? 0;

  // Filter employees who meet minimum rating threshold
  const eligibleEmployees = data.filter((row) => {
    const rating = parseFloat(String(row[options.serviceRatingColumn!]));
    return !isNaN(rating) && rating >= minimumRating;
  });

  if (eligibleEmployees.length === 0) {
    console.warn('No employees meet the minimum rating threshold');
    return data.map((row) => ({
      'Employee Name': row['Employee Name'],
      'Original Hours': row['Hours Worked'],
      'Original Sales': row['Sales Generated'],
      'Calculated Tip': '0.00',
    }));
  }

  // Calculate totals and ranges
  let totalSales = 0;
  let minRating = Infinity,
    maxRating = -Infinity;
  let minHours = Infinity,
    maxHours = -Infinity;

  eligibleEmployees.forEach((row) => {
    const sales = parseFloat(String(row['Sales Generated']));
    const hours = parseFloat(String(row['Hours Worked']));
    const rating = parseFloat(String(row[options.serviceRatingColumn!]));

    if (!isNaN(sales)) totalSales += sales;
    if (!isNaN(hours)) {
      minHours = Math.min(minHours, hours);
      maxHours = Math.max(maxHours, hours);
    }
    if (!isNaN(rating)) {
      minRating = Math.min(minRating, rating);
      maxRating = Math.max(maxRating, rating);
    }
  });

  let totalTipPool = options.totalTipPool || 0;
  if (totalTipPool === 0 && options.tipPercentage !== undefined) {
    totalTipPool = calculateTotalTipPoolFromSales(totalSales, options.tipPercentage);
  }

  // Calculate weighted scores
  const employeeScores = eligibleEmployees.map((row) => {
    const hours = parseFloat(String(row['Hours Worked']));
    const rating = parseFloat(String(row[options.serviceRatingColumn!]));

    const normalizedHours = !isNaN(hours) ? normalizeValue(hours, minHours, maxHours) : 0;
    const normalizedRating = !isNaN(rating) ? normalizeValue(rating, minRating, maxRating) : 0;

    const weightedScore = normalizedHours * hoursWeight + normalizedRating * serviceWeight;

    return {
      employeeName: row['Employee Name'],
      hours,
      sales: parseFloat(String(row['Sales Generated'])),
      weightedScore,
    };
  });

  const totalWeightedScore = employeeScores.reduce((sum, emp) => sum + emp.weightedScore, 0);

  return data.map((row) => {
    const employeeName = row['Employee Name'];
    const hours = parseFloat(String(row['Hours Worked']));
    const sales = parseFloat(String(row['Sales Generated']));

    const employeeScore = employeeScores.find((emp) => emp.employeeName === employeeName);
    let calculatedTip = 0;

    if (employeeScore && totalWeightedScore > 0) {
      calculatedTip = (employeeScore.weightedScore / totalWeightedScore) * totalTipPool;
      calculatedTip = applyTipConstraints(calculatedTip, hours, options);
    }

    return {
      'Employee Name': employeeName,
      'Original Hours': isNaN(hours) ? 'N/A' : hours,
      'Original Sales': isNaN(sales) ? 'N/A' : sales,
      'Calculated Tip': calculatedTip.toFixed(2),
    };
  });
};

/**
 * Enhanced calculation result that includes summary and fairness metrics
 */
export interface EnhancedCalculationResult {
  results: CalculatedTipResult[];
  summary: TipCalculationSummary;
  fairness: FairnessMetrics;
  validation: DataValidationResult;
}

/**
 * Enhanced main dispatcher function that includes analytics and validation
 * @param {EmployeeData[]} data - The parsed Excel data.
 * @param {string} formulaType - The type of formula to use.
 * @param {FormulaOptions} options - Optional parameters specific to the chosen formula.
 * @returns {EnhancedCalculationResult} Enhanced results with analytics.
 */
export const calculateTipsWithAnalytics = (
  data: EmployeeData[],
  formulaType: string = 'proportionalByHours',
  options: FormulaOptions = {},
): EnhancedCalculationResult => {
  // Validate data quality first
  const validation = validateDataQuality(data);

  if (!validation.isValid) {
    return {
      results: [],
      summary: {
        totalTipPool: 0,
        averageTip: 0,
        medianTip: 0,
        tipRange: { min: 0, max: 0 },
        employeesAffectedByConstraints: 0,
      },
      fairness: {
        giniCoefficient: 0,
        tipPerHourVariance: 0,
        roleFairnessScore: 0,
        shiftFairnessScore: 0,
      },
      validation,
    };
  }

  // Calculate tips using existing function
  const results = calculateTips(data, formulaType, options);

  // Generate analytics
  const summary = generateCalculationSummary(results, data, options);
  const fairness = analyzeFairness(results, data);

  return {
    results,
    summary,
    fairness,
    validation,
  };
};

/**
 * Main dispatcher function to calculate tips based on the chosen formula type.
 * @param {EmployeeData[]} data - The parsed Excel data.
 * @param {string} formulaType - The type of formula to use.
 * @param {FormulaOptions} options - Optional parameters specific to the chosen formula.
 * @returns {CalculatedTipResult[]} An array of objects with calculated tip results.
 */
export const calculateTips = (
  data: EmployeeData[],
  formulaType: string = 'proportionalByHours',
  options: FormulaOptions = {},
): CalculatedTipResult[] => {
  if (!data || data.length === 0) {
    return [];
  }

  // Validate essential headers once at the top level
  if (!validateRequiredHeaders(data)) {
    console.error(
      'Missing required columns: "Employee Name", "Hours Worked", or "Sales Generated".',
    );
    return [];
  }

  let results: CalculatedTipResult[] = [];

  switch (formulaType) {
    case 'proportionalByHours':
      options.tipPercentage = options.tipPercentage ?? 0.05;
      results = calculatePoolByHours(data, options);
      break;
    case 'fixedAmount':
      results = calculatePoolFixedAmount(data, options);
      break;
    case 'pointsSystem':
      results = calculatePoolByPoints(data, options);
      break;
    case 'directPercentage':
      options.tipPercentage = options.tipPercentage ?? 0.1;
      results = calculateSplitDirectPercentage(data, options);
      break;
    case 'weightedDistribution':
      options.tipPercentage = options.tipPercentage ?? 0.05;
      results = calculateWeightedDistribution(data, options);
      break;
    case 'roleBasedTips':
      options.tipPercentage = options.tipPercentage ?? 0.05;
      results = calculateRoleBasedTips(data, options);
      break;
    case 'shiftBasedTips':
      options.tipPercentage = options.tipPercentage ?? 0.05;
      results = calculateShiftBasedTips(data, options);
      break;
    case 'performanceBasedTips':
      options.tipPercentage = options.tipPercentage ?? 0.05;
      results = calculatePerformanceBasedTips(data, options);
      break;
    case 'sectionBasedTips':
      options.tipPercentage = options.tipPercentage ?? 0.05;
      results = calculateSectionBasedTips(data, options);
      break;
    case 'serviceRatingBasedTips':
      options.tipPercentage = options.tipPercentage ?? 0.05;
      results = calculateServiceRatingBasedTips(data, options);
      break;
    default:
      console.warn(`Unknown formula type: ${formulaType}. Falling back to 'proportionalByHours'.`);
      options.tipPercentage = options.tipPercentage ?? 0.05;
      results = calculatePoolByHours(data, options);
      break;
  }

  return results;
};
