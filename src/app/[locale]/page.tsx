'use client'; // This directive is necessary for client components in Next.js App Router

import React, { useState, useRef, ChangeEvent, useEffect, useCallback } from 'react';
import { useTranslations } from 'next-intl';
// Import the tip calculation helper functions and interfaces
import {
  calculateTipsWithAnalytics,
  EmployeeData,
  CalculatedTipResult,
  FormulaOptions,
  TipCalculationSummary,
  FairnessMetrics,
  DataValidationResult
} from '../../lib/utils/tipCalculationHelper'; // Adjust path as needed

import * as XLSX from 'xlsx';
import LanguageSwitcher from '../../components/LanguageSwitcher';
// For a Next.js project, you would install xlsx: npm install xlsx
// For Canvas environment, assume XLSX is globally available via CDN.
//declare var XLSX: any; // Declare XLSX to avoid TypeScript errors in Canvas

interface CustomModalProps {
  message: string;
  onClose: () => void;
  show: boolean;
}

// Custom Modal Component (instead of alert/confirm)
const CustomModal: React.FC<CustomModalProps> = ({ message, onClose, show }) => {
  const t = useTranslations('common');

  if (!show) return null;
  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-center z-50 p-4">
      <div className="bg-white p-6 rounded-xl shadow-2xl max-w-sm w-full text-center border border-gray-200">
        <p className="text-lg font-semibold mb-4 text-gray-800">{message}</p>
        <button
          onClick={onClose}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out shadow-md"
        >
          {t('ok')}
        </button>
      </div>
    </div>
  );
};

export default function Home() {
  const t = useTranslations();
  const [excelData, setExcelData] = useState<EmployeeData[]>([]);
  const [calculatedTips, setCalculatedTips] = useState<CalculatedTipResult[]>([]);
  const [fileName, setFileName] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showModal, setShowModal] = useState<boolean>(false);
  const [modalMessage, setModalMessage] = useState<string>('');

  // New analytics state
  const [calculationSummary, setCalculationSummary] = useState<TipCalculationSummary | null>(null);
  const [fairnessMetrics, setFairnessMetrics] = useState<FairnessMetrics | null>(null);
  const [dataValidation, setDataValidation] = useState<DataValidationResult | null>(null);
  const [showAnalytics, setShowAnalytics] = useState<boolean>(false);

  // State for formula selection and options
  const [selectedFormula, setSelectedFormula] = useState<string>('proportionalByHours');
  const [formulaOptions, setFormulaOptions] = useState<FormulaOptions>({
    // Basic options
    tipPercentage: 0.05,
    fixedAmountPerEmployee: 100,
    pointsColumn: 'Points',
    pointFactorColumns: [],

    // Enhanced options with defaults
    hoursWeight: 0.4,
    salesWeight: 0.4,
    performanceWeight: 0.2,
    performanceColumn: '',

    // Role-based options
    roleTiers: {
      'Manager': { multiplier: 1.5, minimumTip: 50, maximumTip: 200 },
      'Server': { multiplier: 1.0, minimumTip: 20, maximumTip: 150 },
      'Busser': { multiplier: 0.8, minimumTip: 15, maximumTip: 100 },
      'Host': { multiplier: 0.7, minimumTip: 10, maximumTip: 80 }
    },
    roleColumn: '',

    // Shift-based options
    shiftMultipliers: {
      morning: 0.8,
      lunch: 1.0,
      dinner: 1.3,
      late: 1.1
    },
    shiftColumn: '',

    // Performance thresholds
    performanceThresholds: {
      excellent: { threshold: 1000, bonus: 0.2 },
      good: { threshold: 500, bonus: 0.1 }
    },
    performanceMetric: 'sales',

    // Min/Max constraints
    minimumTipPerEmployee: 0,
    maximumTipPerEmployee: 0,
    minimumTipPerHour: 0,

    // Section allocations
    sectionColumn: '',
    sectionAllocations: {
      'Dining Room': 0.6,
      'Bar': 0.3,
      'Kitchen': 0.1
    },

    // Service rating options
    serviceRatingColumn: '',
    serviceRatingWeight: 0.3,
    minimumRatingThreshold: 3.0
  });
  const [availableExcelColumns, setAvailableExcelColumns] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleShowModal = (message: string) => {
    setModalMessage(message);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setModalMessage('');
  };

  // Function to clear all file-related states
  const handleClearFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = ''; // Clear the file input element
    }
    setExcelData([]);
    setCalculatedTips([]);
    setFileName('');
    setErrorMessage('');
    setAvailableExcelColumns([]);
    // Optionally reset formula options to default if desired after clearing file
    setFormulaOptions({
      // Basic options
      tipPercentage: 0.05,
      fixedAmountPerEmployee: 100,
      pointsColumn: 'Points',
      pointFactorColumns: [],

      // Enhanced options with defaults
      hoursWeight: 0.4,
      salesWeight: 0.4,
      performanceWeight: 0.2,
      performanceColumn: '',

      // Role-based options
      roleTiers: {
        'Manager': { multiplier: 1.5, minimumTip: 50, maximumTip: 200 },
        'Server': { multiplier: 1.0, minimumTip: 20, maximumTip: 150 },
        'Busser': { multiplier: 0.8, minimumTip: 15, maximumTip: 100 },
        'Host': { multiplier: 0.7, minimumTip: 10, maximumTip: 80 }
      },
      roleColumn: '',

      // Shift-based options
      shiftMultipliers: {
        morning: 0.8,
        lunch: 1.0,
        dinner: 1.3,
        late: 1.1
      },
      shiftColumn: '',

      // Performance thresholds
      performanceThresholds: {
        excellent: { threshold: 1000, bonus: 0.2 },
        good: { threshold: 500, bonus: 0.1 }
      },
      performanceMetric: 'sales',

      // Min/Max constraints
      minimumTipPerEmployee: 0,
      maximumTipPerEmployee: 0,
      minimumTipPerHour: 0,

      // Section allocations
      sectionColumn: '',
      sectionAllocations: {
        'Dining Room': 0.6,
        'Bar': 0.3,
        'Kitchen': 0.1
      },

      // Service rating options
      serviceRatingColumn: '',
      serviceRatingWeight: 0.3,
      minimumRatingThreshold: 3.0
    });
    setSelectedFormula('proportionalByHours'); // Reset to default formula
  };

  // Function to handle file upload
  const handleFileUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      handleClearFile(); // Clear states if no file is selected (e.g., user cancels)
      setErrorMessage('No file selected.');
      return;
    }

    setFileName(file.name);
    setIsProcessing(true);
    setErrorMessage('');
    setExcelData([]);
    setCalculatedTips([]);
    setAvailableExcelColumns([]);

    const reader = new FileReader();

    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const json = XLSX.utils.sheet_to_json(worksheet, { header: 1 }); // Get data as an array of arrays

        if (json.length === 0) {
          handleShowModal(t('common.emptyFile'));
          setIsProcessing(false);
          handleClearFile();
          return;
        }

        const headers: string[] = json[0] as string[];
        const rows = json.slice(1);

        const parsedData: EmployeeData[] = rows.map(row => {
          const rowObject: { [key: string]: unknown } = {};
          headers.forEach((header, index) => {
            rowObject[header] = (row as unknown[])[index];
          });
          return rowObject as EmployeeData;
        });

        setExcelData(parsedData);
        setAvailableExcelColumns(headers); // Store headers for dynamic column selection

        // Automatically calculate tips after file upload
        handleCalculateTips(parsedData, selectedFormula, formulaOptions);

      } catch (error) {
        console.error('Error reading Excel file:', error);
        setErrorMessage(t('common.failedToRead'));
        handleShowModal(t('common.failedToRead'));
        handleClearFile();
      } finally {
        setIsProcessing(false);
      }
    };

    reader.onerror = (error) => {
      console.error('File reader error:', error);
      setErrorMessage(t('common.errorReading'));
      handleShowModal(t('common.errorReading'));
      setIsProcessing(false);
      handleClearFile();
    };

    reader.readAsArrayBuffer(file);
  };

  // Wrap handleCalculateTips in useCallback to prevent it from changing on every render
  const handleCalculateTips = useCallback((data: EmployeeData[], formula: string, options: FormulaOptions) => {
    if (data.length === 0) {
      setCalculatedTips([]);
      setCalculationSummary(null);
      setFairnessMetrics(null);
      setDataValidation(null);
      return;
    }

    setErrorMessage(''); // Clear previous errors
    try {
      // Specific validation for points system
      if (formula === 'pointsSystem') {
        if (!options.pointFactorColumns || options.pointFactorColumns.length === 0) {
          throw new Error(t('errors.pointsSystemSelectColumns'));
        }

        // Validate that selected columns exist in the uploaded Excel file
        const missingColumns = options.pointFactorColumns.filter(
          (col) => !availableExcelColumns.includes(col)
        );
        if (missingColumns.length > 0) {
          throw new Error(`${t('errors.pointsSystemMissingColumns')} ${missingColumns.join(', ')}.`);
        }
      }

      // Use enhanced calculation with analytics
      const enhancedResults = calculateTipsWithAnalytics(data, formula, options);
      
      // Update all state with enhanced results
      setCalculatedTips(enhancedResults.results);
      setCalculationSummary(enhancedResults.summary);
      setFairnessMetrics(enhancedResults.fairness);
      setDataValidation(enhancedResults.validation);

      // Show warnings if any
      if (enhancedResults.validation.warnings.length > 0) {
        console.warn('Data validation warnings:', enhancedResults.validation.warnings);
      }

      if (enhancedResults.results.length === 0 && data.length > 0) {
        // This likely means there was a validation error within calculateTips (e.g., missing essential headers)
        setErrorMessage(t('common.couldNotCalculate'));
        handleShowModal(t('common.couldNotCalculate'));
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error during tip calculation:', error);
        setErrorMessage(`${t('common.calculationError')} ${error.message}`);
        handleShowModal(`${t('common.calculationError')} ${error.message}`);
      } else {
        console.error('Unknown error during tip calculation:', error);
        setErrorMessage(t('common.unknownError'));
        handleShowModal(t('common.unknownError'));
      }
      setCalculatedTips([]);
      setCalculationSummary(null);
      setFairnessMetrics(null);
    }
  }, [availableExcelColumns]);

  // Recalculate tips when formula or options change, if data is already loaded
  useEffect(() => {
    if (excelData.length > 0) {
      handleCalculateTips(excelData, selectedFormula, formulaOptions);
    }
  }, [selectedFormula, formulaOptions, excelData, handleCalculateTips]); // Add handleCalculateTips to the dependency array

  // Function to export calculated tips to Excel
  const exportToExcel = () => {
    if (calculatedTips.length === 0) {
      handleShowModal(t('common.noData'));
      return;
    }

    setIsProcessing(true);
    try {
      const ws = XLSX.utils.json_to_sheet(calculatedTips);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Calculated Tips');

      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });

      // Create a temporary link and click it to download
      const downloadLink = document.createElement('a');
      downloadLink.href = URL.createObjectURL(dataBlob);
      downloadLink.download = `Calculated_Tips_${Date.now()}.xlsx`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(downloadLink.href); // Clean up the URL object

      handleShowModal(t('common.successfulExport'));
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      setErrorMessage(t('common.failedExport'));
      handleShowModal(t('common.failedExport'));
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle changes in formula options (e.g., input fields)
  const handleFormulaOptionChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormulaOptions(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) : value
    }));
  };

  // Handle changes for multi-select point factor columns
  const handlePointFactorColumnsChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);
    setFormulaOptions(prev => ({
      ...prev,
      pointFactorColumns: selectedOptions
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 font-sans text-gray-900 p-4 md:p-8 flex flex-col items-center">
      <CustomModal message={modalMessage} onClose={handleCloseModal} show={showModal} />

      <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 w-full max-w-5xl border border-gray-200">
        <div className="flex justify-end mb-4">
          <LanguageSwitcher />
        </div>
        <h1 className="text-3xl md:text-4xl font-extrabold text-center text-blue-700 mb-6">
          {t('page.title')}
        </h1>
        <p className="text-center text-gray-600 mb-8">
          {t('page.description')}
        </p>

        {/* File Upload Section */}
        <div className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg shadow-sm flex flex-col items-center">
          <label htmlFor="file-upload" className="block text-lg font-semibold text-blue-800 mb-4">
            {t('fileUpload.label')}
          </label>
          <div className="flex flex-col sm:flex-row items-center w-full gap-3">
            <input
              id="file-upload"
              type="file"
              accept=".xlsx, .xls"
              onChange={handleFileUpload}
              ref={fileInputRef}
              className="block w-full text-sm text-gray-500
                         file:mr-4 file:py-2 file:px-4
                         file:rounded-full file:border-0
                         file:text-sm file:font-semibold
                         file:bg-blue-100 file:text-blue-700
                         hover:file:bg-blue-200 cursor-pointer"
            />
            {fileName && (
              <button
                onClick={handleClearFile}
                className="mt-2 sm:mt-0 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition duration-150 ease-in-out shadow-sm text-sm"
              >
                {t('common.clearFile')}
              </button>
            )}
          </div>
          {fileName && <p className="mt-2 text-sm text-gray-600">{t('common.selectedFile')} <span className="font-medium">{fileName}</span></p>}
          {isProcessing && (
            <div className="mt-4 flex items-center text-blue-600">
              <svg className="animate-spin h-5 w-5 mr-3 text-blue-500" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t('common.processing')}
            </div>
          )}
          {errorMessage && (
            <p className="mt-4 text-red-600 text-sm font-medium">{errorMessage}</p>
          )}
        </div>

        {/* Formula Selection and Options */}
        {excelData.length > 0 && (
          <div className="mb-8 p-6 bg-gray-100 border border-gray-200 rounded-lg shadow-sm">
            <h2 className="text-xl font-bold text-gray-800 mb-4">{t('formula.title')}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Formula Type Selection */}
              <div>
                <label htmlFor="formula-select" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('formula.selectLabel')}
                </label>
                <select
                  id="formula-select"
                  value={selectedFormula}
                  onChange={(e) => setSelectedFormula(e.target.value)}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md shadow-sm"
                >
                  <optgroup label={t('formula.basicMethods')}>
                    <option value="proportionalByHours">{t('formula.proportionalByHours')}</option>
                    <option value="fixedAmount">{t('formula.fixedAmount')}</option>
                    <option value="pointsSystem">{t('formula.pointsSystem')}</option>
                    <option value="directPercentage">{t('formula.directPercentage')}</option>
                  </optgroup>
                  <optgroup label={t('formula.enhancedMethods')}>
                    <option value="weightedDistribution">{t('formula.weightedDistribution')}</option>
                    <option value="roleBasedTips">{t('formula.roleBasedTips')}</option>
                    <option value="shiftBasedTips">{t('formula.shiftBasedTips')}</option>
                    <option value="performanceBasedTips">{t('formula.performanceBasedTips')}</option>
                    <option value="sectionBasedTips">{t('formula.sectionBasedTips')}</option>
                    <option value="serviceRatingBasedTips">{t('formula.serviceRatingBasedTips')}</option>
                  </optgroup>
                </select>
              </div>

              {/* Dynamic Formula Options */}
              <div className="p-4 border border-gray-300 rounded-md bg-white">
                <h3 className="text-md font-semibold text-gray-800 mb-3">{t('formula.specificOptions')}</h3>
                {selectedFormula === 'proportionalByHours' && (
                  <div>
                    <label htmlFor="tipPercentage" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('form.tipPercentage')}
                    </label>
                    <input
                      type="number"
                      id="tipPercentage"
                      name="tipPercentage"
                      value={formulaOptions.tipPercentage ?? ''}
                      onChange={handleFormulaOptionChange}
                      step="0.01"
                      min="0"
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                  </div>
                )}

                {selectedFormula === 'fixedAmount' && (
                  <div>
                    <label htmlFor="fixedAmountPerEmployee" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('form.fixedAmountPerEmployee')}
                    </label>
                    <input
                      type="number"
                      id="fixedAmountPerEmployee"
                      name="fixedAmountPerEmployee"
                      value={formulaOptions.fixedAmountPerEmployee ?? ''}
                      onChange={handleFormulaOptionChange}
                      step="0.01"
                      min="0"
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                  </div>
                )}

                {selectedFormula === 'pointsSystem' && (
                  <div>
                    <label htmlFor="totalTipPoolForPoints" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('form.totalTipPool')}
                    </label>
                    <input
                      type="number"
                      id="totalTipPoolForPoints"
                      name="totalTipPool" // Matches FormulaOptions property
                      value={formulaOptions.totalTipPool ?? ''}
                      onChange={handleFormulaOptionChange}
                      step="0.01"
                      min="0"
                      placeholder={t('form.totalTipPoolPlaceholder')}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm mb-3"
                    />

                    <label htmlFor="pointFactorColumns" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('form.pointFactorColumns')}
                    </label>
                    <select
                      id="pointFactorColumns"
                      name="pointFactorColumns"
                      multiple
                      value={formulaOptions.pointFactorColumns || []}
                      onChange={handlePointFactorColumnsChange}
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md shadow-sm h-32 overflow-y-auto"
                    >
                      {availableExcelColumns.length > 0 ? (
                        availableExcelColumns.map(col => (
                          <option key={col} value={col}>{col}</option>
                        ))
                      ) : (
                        <option disabled>{t('common.uploadColumns')}</option>
                      )}
                    </select>
                    <p className="mt-2 text-xs text-gray-500">
                      {t('form.pointFactorNote')}
                    </p>
                  </div>
                )}

                {selectedFormula === 'directPercentage' && (
                  <div>
                    <label htmlFor="tipPercentageDirect" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('form.directPercentageLabel')}
                    </label>
                    <input
                      type="number"
                      id="tipPercentageDirect"
                      name="tipPercentage" // Re-uses tipPercentage property
                      value={formulaOptions.tipPercentage ?? ''}
                      onChange={handleFormulaOptionChange}
                      step="0.01"
                      min="0"
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                  </div>
                )}

                {selectedFormula === 'weightedDistribution' && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-3">
                      <div>
                        <label htmlFor="hoursWeight" className="block text-sm font-medium text-gray-700 mb-1">
                          Hours Weight:
                        </label>
                        <input
                          type="number"
                          id="hoursWeight"
                          name="hoursWeight"
                          value={formulaOptions.hoursWeight ?? ''}
                          onChange={handleFormulaOptionChange}
                          step="0.1"
                          min="0"
                          max="1"
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label htmlFor="salesWeight" className="block text-sm font-medium text-gray-700 mb-1">
                          Sales Weight:
                        </label>
                        <input
                          type="number"
                          id="salesWeight"
                          name="salesWeight"
                          value={formulaOptions.salesWeight ?? ''}
                          onChange={handleFormulaOptionChange}
                          step="0.1"
                          min="0"
                          max="1"
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label htmlFor="performanceWeight" className="block text-sm font-medium text-gray-700 mb-1">
                          Performance Weight:
                        </label>
                        <input
                          type="number"
                          id="performanceWeight"
                          name="performanceWeight"
                          value={formulaOptions.performanceWeight ?? ''}
                          onChange={handleFormulaOptionChange}
                          step="0.1"
                          min="0"
                          max="1"
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>
                    <div>
                      <label htmlFor="performanceColumn" className="block text-sm font-medium text-gray-700 mb-1">
                        Performance Column (optional):
                      </label>
                      <select
                        id="performanceColumn"
                        name="performanceColumn"
                        value={formulaOptions.performanceColumn ?? ''}
                        onChange={handleFormulaOptionChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="">Select Performance Column</option>
                        {availableExcelColumns.map(col => (
                          <option key={col} value={col}>{col}</option>
                        ))}
                      </select>
                    </div>
                    <p className="text-xs text-gray-600">
                      Weights should sum to 1.0. Combines hours worked, sales generated, and optional performance metrics.
                    </p>
                  </div>
                )}

                {selectedFormula === 'roleBasedTips' && (
                  <div>
                    <label htmlFor="roleColumn" className="block text-sm font-medium text-gray-700 mb-1">
                      Role Column:
                    </label>
                    <select
                      id="roleColumn"
                      name="roleColumn"
                      value={formulaOptions.roleColumn ?? ''}
                      onChange={handleFormulaOptionChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="">Select Role Column</option>
                      {availableExcelColumns.map(col => (
                        <option key={col} value={col}>{col}</option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-600 mt-2">
                      Default role multipliers: Manager (1.5x), Server (1.0x), Busser (0.8x), Host (0.7x)
                    </p>
                  </div>
                )}

                {selectedFormula === 'shiftBasedTips' && (
                  <div>
                    <label htmlFor="shiftColumn" className="block text-sm font-medium text-gray-700 mb-1">
                      Shift Column:
                    </label>
                    <select
                      id="shiftColumn"
                      name="shiftColumn"
                      value={formulaOptions.shiftColumn ?? ''}
                      onChange={handleFormulaOptionChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="">Select Shift Column</option>
                      {availableExcelColumns.map(col => (
                        <option key={col} value={col}>{col}</option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-600 mt-2">
                      Default shift multipliers: Morning (0.8x), Lunch (1.0x), Dinner (1.3x), Late (1.1x)
                    </p>
                  </div>
                )}

                {selectedFormula === 'performanceBasedTips' && (
                  <div className="space-y-3">
                    <div>
                      <label htmlFor="performanceMetric" className="block text-sm font-medium text-gray-700 mb-1">
                        Performance Metric:
                      </label>
                      <select
                        id="performanceMetric"
                        name="performanceMetric"
                        value={formulaOptions.performanceMetric ?? 'sales'}
                        onChange={handleFormulaOptionChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="sales">Sales Generated</option>
                        <option value="efficiency">Sales per Hour</option>
                        <option value="hours">Hours Worked</option>
                        <option value="custom">Custom Column</option>
                      </select>
                    </div>
                    {formulaOptions.performanceMetric === 'custom' && (
                      <div>
                        <label htmlFor="performanceColumnCustom" className="block text-sm font-medium text-gray-700 mb-1">
                          Custom Performance Column:
                        </label>
                        <select
                          id="performanceColumnCustom"
                          name="performanceColumn"
                          value={formulaOptions.performanceColumn ?? ''}
                          onChange={handleFormulaOptionChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          <option value="">Select Performance Column</option>
                          {availableExcelColumns.map(col => (
                            <option key={col} value={col}>{col}</option>
                          ))}
                        </select>
                      </div>
                    )}
                    <p className="text-xs text-gray-600">
                      Adds bonuses: Excellent performance (≥$1000) gets 20% bonus, Good performance (≥$500) gets 10% bonus
                    </p>
                  </div>
                )}

                {selectedFormula === 'sectionBasedTips' && (
                  <div>
                    <label htmlFor="sectionColumn" className="block text-sm font-medium text-gray-700 mb-1">
                      Section Column:
                    </label>
                    <select
                      id="sectionColumn"
                      name="sectionColumn"
                      value={formulaOptions.sectionColumn ?? ''}
                      onChange={handleFormulaOptionChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="">Select Section Column</option>
                      {availableExcelColumns.map(col => (
                        <option key={col} value={col}>{col}</option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-600 mt-2">
                      Default allocations: Dining Room (60%), Bar (30%), Kitchen (10%)
                    </p>
                  </div>
                )}

                {selectedFormula === 'serviceRatingBasedTips' && (
                  <div className="space-y-3">
                    <div>
                      <label htmlFor="serviceRatingColumn" className="block text-sm font-medium text-gray-700 mb-1">
                        Service Rating Column:
                      </label>
                      <select
                        id="serviceRatingColumn"
                        name="serviceRatingColumn"
                        value={formulaOptions.serviceRatingColumn ?? ''}
                        onChange={handleFormulaOptionChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="">Select Rating Column</option>
                        {availableExcelColumns.map(col => (
                          <option key={col} value={col}>{col}</option>
                        ))}
                      </select>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label htmlFor="serviceRatingWeight" className="block text-sm font-medium text-gray-700 mb-1">
                          Rating Weight (0-1):
                        </label>
                        <input
                          type="number"
                          id="serviceRatingWeight"
                          name="serviceRatingWeight"
                          value={formulaOptions.serviceRatingWeight ?? ''}
                          onChange={handleFormulaOptionChange}
                          step="0.1"
                          min="0"
                          max="1"
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label htmlFor="minimumRatingThreshold" className="block text-sm font-medium text-gray-700 mb-1">
                          Minimum Rating:
                        </label>
                        <input
                          type="number"
                          id="minimumRatingThreshold"
                          name="minimumRatingThreshold"
                          value={formulaOptions.minimumRatingThreshold ?? ''}
                          onChange={handleFormulaOptionChange}
                          step="0.1"
                          min="0"
                          max="5"
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>
                    <p className="text-xs text-gray-600">
                      Combines hours worked with customer service ratings. Only employees meeting minimum rating qualify.
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Global Constraints Section */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-md font-semibold text-blue-800 mb-3">{t('constraints.title')}</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="minimumTipPerEmployee" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('constraints.minimumTipPerEmployee')}
                  </label>
                  <input
                    type="number"
                    id="minimumTipPerEmployee"
                    name="minimumTipPerEmployee"
                    value={formulaOptions.minimumTipPerEmployee ?? ''}
                    onChange={handleFormulaOptionChange}
                    step="0.01"
                    min="0"
                    placeholder={t('constraints.noMinimum')}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="maximumTipPerEmployee" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('constraints.maximumTipPerEmployee')}
                  </label>
                  <input
                    type="number"
                    id="maximumTipPerEmployee"
                    name="maximumTipPerEmployee"
                    value={formulaOptions.maximumTipPerEmployee ?? ''}
                    onChange={handleFormulaOptionChange}
                    step="0.01"
                    min="0"
                    placeholder={t('constraints.noMaximum')}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="minimumTipPerHour" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('constraints.minimumTipPerHour')}
                  </label>
                  <input
                    type="number"
                    id="minimumTipPerHour"
                    name="minimumTipPerHour"
                    value={formulaOptions.minimumTipPerHour ?? ''}
                    onChange={handleFormulaOptionChange}
                    step="0.01"
                    min="0"
                    placeholder={t('constraints.noMinimum')}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              <p className="text-xs text-gray-600 mt-2">
                {t('constraints.note')}
              </p>
            </div>
          </div>
        )}


        {/* Display Results Section */}
        {calculatedTips.length > 0 && (
          <div className="mt-8 space-y-6">
            {/* Analytics Toggle */}
            <div className="flex justify-center">
              <button
                onClick={() => setShowAnalytics(!showAnalytics)}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition duration-150 ease-in-out"
              >
                {showAnalytics ? t('analytics.hideAnalytics') : t('analytics.showAnalytics')}
              </button>
            </div>

            {/* Analytics Section */}
            {showAnalytics && calculationSummary && fairnessMetrics && (
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-indigo-800 mb-4 text-center">{t('analytics.title')}</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  {/* Summary Cards */}
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">{t('analytics.totalTipPool')}</h4>
                    <p className="text-2xl font-bold text-green-600">${calculationSummary.totalTipPool.toFixed(2)}</p>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">{t('analytics.averageTip')}</h4>
                    <p className="text-2xl font-bold text-blue-600">${calculationSummary.averageTip.toFixed(2)}</p>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">{t('analytics.medianTip')}</h4>
                    <p className="text-2xl font-bold text-purple-600">${calculationSummary.medianTip.toFixed(2)}</p>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">{t('analytics.tipRange')}</h4>
                    <p className="text-lg font-bold text-orange-600">
                      ${calculationSummary.tipRange.min.toFixed(2)} - ${calculationSummary.tipRange.max.toFixed(2)}
                    </p>
                  </div>
                </div>

                {/* Fairness Metrics */}
                <div className="bg-white p-4 rounded-lg shadow mb-4">
                  <h4 className="text-lg font-semibold text-gray-800 mb-3">🎯 Fairness Analysis</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-gray-600">Equality Score (Gini Coefficient):</span>
                      <div className="flex items-center mt-1">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${fairnessMetrics.giniCoefficient < 0.3 ? 'bg-green-500' : fairnessMetrics.giniCoefficient < 0.5 ? 'bg-yellow-500' : 'bg-red-500'}`}
                            style={{ width: `${Math.min(fairnessMetrics.giniCoefficient * 100, 100)}%` }}
                          ></div>
                        </div>
                        <span className="ml-2 text-sm font-medium">
                          {fairnessMetrics.giniCoefficient < 0.3 ? 'Very Fair' : fairnessMetrics.giniCoefficient < 0.5 ? 'Moderately Fair' : 'Less Fair'}
                        </span>
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Tip Per Hour Variance:</span>
                      <p className="text-lg font-medium">${fairnessMetrics.tipPerHourVariance.toFixed(2)}</p>
                    </div>
                  </div>
                </div>

                {/* Role/Shift Distribution */}
                {(calculationSummary.distributionByRole || calculationSummary.distributionByShift) && (
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">📈 Distribution Breakdown</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {calculationSummary.distributionByRole && (
                        <div>
                          <h5 className="text-md font-medium text-gray-700 mb-2">By Role</h5>
                          {Object.entries(calculationSummary.distributionByRole).map(([role, data]) => (
                            <div key={role} className="flex justify-between text-sm mb-1">
                              <span>{role} ({data.count})</span>
                              <span className="font-medium">${data.totalTips.toFixed(2)}</span>
                            </div>
                          ))}
                        </div>
                      )}
                      {calculationSummary.distributionByShift && (
                        <div>
                          <h5 className="text-md font-medium text-gray-700 mb-2">By Shift</h5>
                          {Object.entries(calculationSummary.distributionByShift).map(([shift, data]) => (
                            <div key={shift} className="flex justify-between text-sm mb-1">
                              <span>{shift} ({data.count})</span>
                              <span className="font-medium">${data.totalTips.toFixed(2)}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Data Validation Warnings */}
                {dataValidation && dataValidation.warnings.length > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4">
                    <h4 className="text-lg font-semibold text-yellow-800 mb-2">⚠️ Data Quality Warnings</h4>
                    <ul className="list-disc list-inside text-sm text-yellow-700">
                      {dataValidation.warnings.map((warning, index) => (
                        <li key={index}>{warning}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Results Table */}
            <div className="p-6 bg-white border border-gray-200 rounded-xl shadow-lg">
              <h2 className="text-2xl font-bold text-gray-800 mb-4 text-center">{t('results.title')}</h2>
            <div className="overflow-x-auto rounded-lg border border-gray-300 shadow-inner">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    {Object.keys(calculatedTips[0]).map((header, index) => (
                      <th
                        key={index}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {calculatedTips.map((row, rowIndex) => (
                    <tr key={rowIndex} className="hover:bg-gray-50">
                      {Object.values(row).map((value, colIndex) => (
                        <td
                          key={colIndex}
                          className="px-6 py-4 whitespace-nowrap text-sm text-gray-800"
                        >
                          {value}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Export Button */}
            <div className="mt-6 text-center">
              <button
                onClick={exportToExcel}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out transform hover:scale-105"
                disabled={isProcessing}
              >
                <svg className="-ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                {t('common.exportToExcel')}
              </button>
            </div>
          </div>
            )
          </div>
        )}

        {/* Instructions/Placeholder for more features */}
        {calculatedTips.length === 0 && !isProcessing && (
          <div className="mt-8 p-6 bg-gray-100 border border-gray-200 rounded-lg text-center text-gray-700 shadow-sm">
            <p className="mb-4">
              {t('fileUpload.instructions')}
            </p>
            <p className="text-sm">
              {t('fileUpload.foundationalNote')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}