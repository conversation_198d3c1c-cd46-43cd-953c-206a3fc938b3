# Quick Start Guide - Restaurant Tip Calculator

Get up and running with the enhanced tip calculator in minutes!

## 🚀 Quick Setup

1. **<PERSON><PERSON> and Install**
   ```bash
   git clone https://github.com/your-username/restaurant-tip-calculator.git
   cd restaurant-tip-calculator
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

3. **Open Application**
   - Navigate to [http://localhost:3000](http://localhost:3000)

## 📊 Try It Now with Sample Data

1. **Download Sample File**
   - Use the included `sample-employee-data-enhanced.xlsx` file
   - Contains 20 employees with all enhanced features

2. **Upload and Test**
   - Click "Choose File" and select the sample Excel file
   - Data will automatically load and calculate with default method

3. **Explore Methods**
   - Try different calculation methods from the dropdown
   - Adjust settings in real-time
   - See results update instantly

## 🎯 5-Minute Feature Tour

### Basic Methods (Start Here)
1. **Proportional by Hours** - Simple time-based distribution
2. **Fixed Amount** - Equal amount for everyone
3. **Direct Percentage** - Percentage of individual sales

### Enhanced Methods (Advanced)
4. **Weighted Distribution** - Combine hours + sales + performance
5. **Role-Based Tips** - Different rates by position
6. **Shift-Based Tips** - Premium for busy shifts
7. **Performance-Based** - Bonuses for high performers
8. **Section-Based** - Separate pools by area
9. **Service Rating-Based** - Customer feedback integration

## 📋 Sample Data Overview

The included sample file has:
- **20 employees** across 4 roles (Manager, Server, Busser, Host)
- **$16,250 total sales** generating $812.50 tip pool (5%)
- **121 total hours** worked
- **4.34 average** service rating
- **All columns** needed for testing every method

## 🛡️ Global Constraints

Set these to ensure fairness:
- **Minimum Tip per Employee**: Guarantee base amount
- **Maximum Tip per Employee**: Cap individual payouts  
- **Minimum Tip per Hour**: Ensure hourly minimums

## 🎨 Method Recommendations

### New Restaurant (Start Simple)
- Use **Proportional by Hours**
- Add **Minimum Tip per Employee**: $25

### Established Restaurant (Balanced)
- Use **Weighted Distribution**
- Hours: 40%, Sales: 40%, Performance: 20%
- Add constraints as needed

### Performance-Focused (Advanced)
- Use **Performance-Based Tips**
- Set thresholds: Good ≥$500, Excellent ≥$1000
- Add **Maximum Tip per Employee** for budget control

## 📁 File Requirements

### Required Columns (All Methods)
- `Employee Name` - Employee identifier
- `Hours Worked` - Numeric hours
- `Sales Generated` - Numeric sales amount

### Optional Columns (Enhanced Methods)
- `Role` - Position (Manager, Server, Busser, Host)
- `Shift` - Time period (Morning, Lunch, Dinner, Late)
- `Section` - Area (Dining Room, Bar, Kitchen)
- `Service Rating` - Customer rating (1-5 scale)
- `Performance` - Custom performance metric
- `*Points` - Various point columns for point system

## 🔧 Troubleshooting

### Common Issues:
- **File won't upload**: Check it's .xlsx or .xls format
- **Missing columns error**: Ensure required columns exist
- **Zero tips calculated**: Check tip percentage > 0
- **Weights warning**: Ensure weights sum to 1.0

### Quick Fixes:
- Use the sample file to test functionality first
- Check column names match exactly (case-sensitive)
- Verify numeric columns contain numbers, not text
- Start with basic methods before trying enhanced ones

## 📖 Next Steps

1. **Read Full Documentation**: Check `README.md` for complete details
2. **See Examples**: Review `USAGE_EXAMPLES.md` for method comparisons
3. **Create Your Data**: Use sample file as template for your restaurant
4. **Customize Settings**: Adjust multipliers and thresholds for your needs

## 🆘 Need Help?

- **Documentation**: README.md and USAGE_EXAMPLES.md
- **Sample Data**: sample-employee-data-enhanced.xlsx
- **Issues**: Open GitHub issue for bugs or questions
- **Features**: Request new calculation methods via GitHub

---

**Ready to revolutionize your tip distribution? Upload the sample file and start exploring!** 🎉
