# Tip Calculation Methods

This document provides an overview of the various tip calculation methods implemented in the `tipCalculationHelper.ts` file. Each method is designed to calculate tips based on different criteria and distribution strategies.

## 1. Proportional by Hours

### Description

Distributes tips proportionally based on the hours worked by each employee. The total tip pool can either be provided explicitly or calculated as a percentage of total sales.

### Parameters

- **data**: Array of employee data.
- **options**:
  - `tipPercentage` (optional): Percentage of total sales to form the tip pool (e.g., 0.05 for 5%).
  - `totalTipPool` (optional): Explicitly provided total tip pool.

### Returns

An array of calculated tip results for each employee.

---

## 2. Fixed Amount per Employee

### Description

Distributes a fixed amount of tips to each eligible employee.

### Parameters

- **data**: Array of employee data.
- **options**:
  - `fixedAmountPerEmployee`: Fixed tip amount for each employee.

### Returns

An array of calculated tip results for each employee.

---

## 3. Point-Based System

### Description

Distributes tips based on a point system. Points can be derived from a single column or summed from multiple columns.

### Parameters

- **data**: Array of employee data.
- **options**:
  - `totalTipPool` (optional): Explicitly provided total tip pool.
  - `tipPercentage` (optional): Percentage of total sales to form the tip pool.
  - `pointsColumn` (optional): Name of the column containing points.
  - `pointFactorColumns` (optional): Array of column names whose values sum up to total points.

### Returns

An array of calculated tip results for each employee.

---

## 4. Direct Percentage of Sales

### Description

Each employee receives a direct percentage of their own sales as tips.

### Parameters

- **data**: Array of employee data.
- **options**:
  - `tipPercentage`: Percentage of individual sales to calculate tips (e.g., 0.1 for 10%).

### Returns

An array of calculated tip results for each employee.

---

## 5. Main Dispatcher Function

### Description

The `calculateTips` function acts as the main dispatcher to calculate tips based on the chosen formula type. It validates the required headers and delegates the calculation to the appropriate method.

### Parameters

- **data**: Array of employee data.
- **formulaType**: The type of formula to use (e.g., `proportionalByHours`, `fixedAmount`, `directPercentage`, `pointsSystem`).
- **options**: Optional parameters specific to the chosen formula.

### Returns

An array of calculated tip results for each employee.

### Supported Formula Types

- `proportionalByHours`
- `fixedAmount`
- `pointsSystem`
- `directPercentage`

---

## Required Excel Format

### For Proportional by Hours

The Excel file should include the following columns:

- **Employee Name**: The name of the employee.
- **Hours Worked**: The total hours worked by the employee.
- **Sales Generated**: The total sales generated by the employee.

### For Fixed Amount per Employee

The Excel file should include the following columns:

- **Employee Name**: The name of the employee.
- **Hours Worked**: The total hours worked by the employee.
- **Sales Generated**: The total sales generated by the employee.

### For Point-Based System

The Excel file should include the following columns:

- **Employee Name**: The name of the employee.
- **Hours Worked**: The total hours worked by the employee.
- **Sales Generated**: The total sales generated by the employee.
- **Points Column**: A column containing points for each employee (if using a single points column).
- **Point Factor Columns**: Multiple columns containing point factors to be summed up (if using multiple point factor columns).

**Note**: Please select at least one column for point factors.

### For Direct Percentage of Sales

The Excel file should include the following columns:

- **Employee Name**: The name of the employee.
- **Hours Worked**: The total hours worked by the employee.
- **Sales Generated**: The total sales generated by the employee.

---

## Sample Excel Format

Below is a sample Excel format for the required columns:

| Employee Name | Hours Worked | Sales Generated | Points Column | Point Factor 1 | Point Factor 2 |
| ------------- | ------------ | --------------- | ------------- | -------------- | -------------- |
| John Doe      | 40           | 5000            | 10            | 5              | 3              |
| Jane Smith    | 35           | 4500            | 8             | 4              | 2              |
| Bob Johnson   | 30           | 4000            | 6             | 3              | 1              |

- **Employee Name**: The name of the employee.
- **Hours Worked**: The total hours worked by the employee.
- **Sales Generated**: The total sales generated by the employee.
- **Points Column**: A single column for points (if applicable).
- **Point Factor 1, Point Factor 2**: Example columns for point factors (if applicable).

For more details, refer to the implementation in the `tipCalculationHelper.ts` file.
