# Changelog (2025-07-29)

## [Unreleased]

### Added

- Highlighted the "Select Columns for Point Factors" dropdown in red if no columns are selected for the "Points System" formula.
- Improved error messaging for the "Points System" formula to guide users better.
- Implemented various tip calculation methods in `tipCalculationHelper.ts`, including:
  - Proportional by hours
  - Fixed amount per employee
  - Point-based system
  - Direct percentage of sales
  - Weighted distribution
  - Role-based tips
  - Shift-based tips
  - Performance-based tips
  - Section-based tips
  - Service rating-based tips
- Created a main dispatcher function `calculateTips` to handle different formula types.
- Added comprehensive documentation in `tipCalculationMethods.md` outlining each method, parameters, return values, and required Excel format.
- **Enhanced Analytics & Validation Features:**
  - Added `calculateTipsWithAnalytics` function providing comprehensive calculation analytics
  - Implemented data quality validation with warnings for potential issues (duplicate names, negative values, missing data)
  - Added calculation summary metrics (total tip pool, average, median, range, fairness analysis)
  - Implemented fairness analysis with Gini coefficient and tip-per-hour variance calculations
  - Added distribution breakdowns by role and shift when applicable
  - Enhanced UI with analytics dashboard showing:
    - Summary cards for key metrics
    - Fairness analysis with visual indicators
    - Role/shift distribution breakdowns
    - Data quality warnings display
    - Toggle-able analytics section for better UX

### Fixed

- Resolved React Hook dependency warnings by wrapping `handleCalculateTips` in `useCallback`.
- Addressed missing column validation for the "Points System" formula.

### Changed

- Enhanced user experience for formula-specific options in the tip calculation UI.
