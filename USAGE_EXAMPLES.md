# Usage Examples - Restaurant Tip Calculator

This document provides detailed examples of how to use each calculation method with the enhanced tip calculator.

## 📋 Sample Data Overview

The included `sample-employee-data-enhanced.xlsx` file contains 20 employees with the following data:

- **Roles**: Manager (2), Server (14), <PERSON>ser (3), Host (2)
- **Shifts**: Morning (1), Lunch (6), Dinner (13)
- **Sections**: Dining Room (13), Bar (6), Kitchen (1)
- **Service Ratings**: Range from 3.8 to 4.9 (5-point scale)
- **Performance Scores**: Range from 65 to 95
- **Total Sales**: $17,650
- **Total Hours**: 118 hours

## 🎯 Method Examples

### 1. Proportional by Hours (Basic)

**Best for**: Equal treatment based on time worked

**Configuration**:

- Tip Percentage: 5% (0.05)

**Result**: Tips distributed proportionally based on hours worked

- 8-hour employees get more than 4-hour employees
- Total tip pool: $882.50 (5% of $17,650)

### 2. Fixed Amount (Basic)

**Best for**: Simple, equal distribution

**Configuration**:

- Fixed Amount per Employee: $40

**Result**: Every employee gets exactly $40

- Total payout: $800 (20 employees × $40)

### 3. Points System (Basic)

**Best for**: Complex multi-factor evaluation

**Configuration**:

- Point Factor Columns: Level Points, Role Points, Work Hours Points, Shift Period Points
- Tip Percentage: 5%

**Result**: Tips based on sum of all point columns

- High-performing managers get the most
- Multi-dimensional evaluation

### 4. Direct Percentage (Basic)

**Best for**: Sales-driven compensation

**Configuration**:

- Tip Percentage: 10% (0.10)

**Result**: Each employee gets 10% of their individual sales

- John Smith (Sales: $1200) gets $120
- Sarah Wilson (Sales: $300) gets $30

### 5. Weighted Distribution (Enhanced)

**Best for**: Balanced evaluation of multiple factors

**Configuration**:

- Hours Weight: 0.4 (40%)
- Sales Weight: 0.4 (40%)
- Performance Weight: 0.2 (20%)
- Performance Column: Performance
- Tip Percentage: 5%

**Result**: Combines hours, sales, and performance scores

- Rewards both time worked and productivity
- Performance bonuses for high scorers

### 6. Role-Based Tips (Enhanced)

**Best for**: Hierarchical restaurant structures

**Configuration**:

- Role Column: Role
- Default multipliers applied:
  - Manager: 1.5x
  - Server: 1.0x
  - Busser: 0.8x
  - Host: 0.7x

**Result**: Tips adjusted by position level

- Mike Johnson (Manager, 8 hours) gets more than John Smith (Server, 8 hours)
- Reflects responsibility differences

### 7. Shift-Based Tips (Enhanced)

**Best for**: Restaurants with varying shift demands

**Configuration**:

- Shift Column: Shift
- Default multipliers:
  - Morning: 0.8x
  - Lunch: 1.0x
  - Dinner: 1.3x
  - Late: 1.1x

**Result**: Dinner shift workers get premium pay

- Dinner servers get 30% more than lunch servers
- Reflects busy period demands

### 8. Performance-Based Tips (Enhanced)

**Best for**: Merit-based compensation

**Configuration**:

- Performance Metric: Sales
- Thresholds:
  - Excellent (≥$1000): 20% bonus
  - Good (≥$500): 10% bonus

**Result**: High performers get bonuses

- Mike Johnson ($1500 sales) gets 20% bonus
- Rachel Kim ($600 sales) gets 10% bonus
- Sarah Wilson ($300 sales) gets base amount

### 9. Section-Based Tips (Enhanced)

**Best for**: Multi-area restaurants

**Configuration**:

- Section Column: Section
- Default allocations:
  - Dining Room: 60%
  - Bar: 30%
  - Kitchen: 10%

**Result**: Tips distributed by restaurant area

- Dining Room pool: $529.50 (13 employees)
- Bar pool: $264.75 (6 employees)
- Kitchen pool: $88.25 (1 employee)

### 10. Service Rating-Based Tips (Enhanced)

**Best for**: Customer service excellence focus

**Configuration**:

- Service Rating Column: Service Rating
- Rating Weight: 0.3 (30%)
- Minimum Rating Threshold: 4.0

**Result**: Customer ratings influence tips

- Maria Rodriguez (4.9 rating) gets premium
- David Lee (3.8 rating) excluded for low rating
- Combines service quality with hours worked

## 🛡️ Global Constraints Examples

### Example 1: Minimum Guarantees

**Configuration**:

- Minimum Tip per Employee: $25
- Minimum Tip per Hour: $3

**Effect**:

- Part-time employees guaranteed at least $25
- All employees get at least $3/hour worked
- Prevents unfairly low payouts

### Example 2: Maximum Caps

**Configuration**:

- Maximum Tip per Employee: $100

**Effect**:

- Prevents excessive individual payouts
- Ensures more equitable distribution
- Useful for budget control

### Example 3: Combined Constraints

**Configuration**:

- Minimum Tip per Employee: $20
- Maximum Tip per Employee: $80
- Minimum Tip per Hour: $2.50

**Effect**:

- Creates a fair range for all employees
- Balances equity with performance rewards
- Suitable for most restaurant types

> **Constraint Logic Update:**
>
> - Minimum/maximum constraints only apply if set to a positive value.
> - Setting a constraint to 0 disables it (does not cap at $0).
> - For example, `Maximum Tip per Employee: 0` will NOT cap tips at $0; it will simply not apply any maximum cap.

## 📊 Comparison Results

Using the sample data with 5% tip pool ($882.50):

| Method                | Highest Tip | Lowest Tip | Range  | Best For            |
| --------------------- | ----------- | ---------- | ------ | ------------------- |
| Proportional by Hours | $59.90      | $21.21     | $38.69 | Time-based fairness |
| Fixed Amount          | $40.00      | $40.00     | $0.00  | Simplicity          |
| Role-Based            | $89.85      | $14.85     | $75.00 | Hierarchy respect   |
| Shift-Based           | $77.87      | $17.66     | $60.21 | Peak hour premiums  |
| Performance-Based     | $71.88      | $21.21     | $50.67 | Merit rewards       |
| Weighted Distribution | $68.45      | $18.92     | $49.53 | Balanced approach   |

## 🎯 Recommended Combinations

### Fine Dining Restaurant

1. **Primary**: Service Rating-Based Tips
2. **Constraints**: Minimum $30/employee, Maximum $120/employee
3. **Why**: Emphasizes customer experience quality

### Sports Bar

1. **Primary**: Shift-Based Tips
2. **Secondary**: Section-Based (Bar vs Kitchen)
3. **Why**: Rewards busy game-day shifts

### Family Restaurant

1. **Primary**: Weighted Distribution (40% hours, 40% sales, 20% service)
2. **Constraints**: Minimum $25/employee
3. **Why**: Balanced approach with service focus

### High-Volume Casual

1. **Primary**: Performance-Based Tips
2. **Constraints**: Maximum $100/employee
3. **Why**: Drives sales performance

## 🔧 Troubleshooting

### Common Issues:

1. **"No employees meet minimum rating threshold"**
   - Lower the minimum rating threshold
   - Check service rating data quality

2. **"Weights sum to X, normalizing to 1.0"**
   - Ensure Hours Weight + Sales Weight + Performance Weight = 1.0

3. **"Missing required columns"**
   - Verify Excel file has: Employee Name, Hours Worked, Sales Generated

4. **"None of the specified point columns found"**
   - Check column names match exactly (case-sensitive)
   - Ensure point columns contain numeric data

5. **"All tips are zero"**
   - Check if Maximum Tip per Employee is set to 0 (should be unset or a positive value)
   - Setting max/min constraints to 0 disables them; does not cap at $0
   - Verify your tip percentage is not 0 (should be 0.05 for 5%, not 5)

6. **"Tips seem too low/high"**
   - Double-check tip percentage format (0.05 = 5%, not 5)
   - Verify sales data is in correct units (dollars, not cents)
   - Check if constraints are inadvertently limiting payouts

### Best Practices:

1. **Test with sample data first**
2. **Start with basic methods before trying enhanced ones**
3. **Use constraints to ensure fairness**
4. **Review results before finalizing**
5. **Keep backup of original data**
6. **Verify tip percentages are in decimal format (0.05 for 5%)**
7. **Set constraints to positive values or leave unset (don't use 0)**
8. **Export results to Excel for record-keeping**

## 💡 Pro Tips

### Debugging Your Setup:

- Always preview results with a small test dataset first
- Use the analytics dashboard to verify total tip pool calculations
- Check the fairness metrics to ensure equitable distribution
- Review individual employee results for outliers

### Common Configuration Mistakes:

- **Tip Percentage**: Use 0.05 for 5%, not 5
- **Constraints**: Use positive values or leave blank; 0 disables the constraint
- **Column Names**: Must match Excel headers exactly (case-sensitive)
- **Data Types**: Ensure hours and sales are numbers, not text

### Performance Optimization:

- Larger datasets (100+ employees) work best with basic methods
- Enhanced methods with multiple factors may be slower but more accurate
- Consider using section-based tips for very large restaurants (200+ employees)

---

**Need help?** Check the main README.md or open an issue on GitHub.
